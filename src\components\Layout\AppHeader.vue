<template>
  <div class="app-header">
    <div class="header-left">
      <div class="logo">
        <el-icon class="logo-icon"><Setting /></el-icon>
        <span class="logo-text">露天矿道路设计</span>
      </div>
    </div>
    
    <div class="header-center">
      <el-menu
        mode="horizontal"
        :default-active="activeIndex"
        class="header-menu"
        @select="handleSelect"
      >
        <el-menu-item index="1">
          <el-icon><Document /></el-icon>
          <span>项目管理</span>
        </el-menu-item>
        <el-menu-item index="2">
          <el-icon><MapLocation /></el-icon>
          <span>地形数据</span>
        </el-menu-item>
        <el-menu-item index="3">
          <el-icon><Connection /></el-icon>
          <span>道路设计</span>
        </el-menu-item>
        <el-menu-item index="4">
          <el-icon><TrendCharts /></el-icon>
          <span>路线优化</span>
        </el-menu-item>
        <el-menu-item index="5">
          <el-icon><Warning /></el-icon>
          <span>安全检测</span>
        </el-menu-item>
        <el-menu-item index="6">
          <el-icon><Files /></el-icon>
          <span>CAD管理</span>
        </el-menu-item>
        <el-menu-item index="7">
          <el-icon><DataAnalysis /></el-icon>
          <span>数据分析</span>
        </el-menu-item>
      </el-menu>
    </div>
    
    <div class="header-right">
      <el-button type="primary" size="small">
        <el-icon><User /></el-icon>
        用户中心
      </el-button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import {
  Setting, Document, MapLocation, Connection,
  TrendCharts, Warning, Files, DataAnalysis, User
} from '@element-plus/icons-vue'

// 定义组件名称
defineOptions({
  name: 'AppHeader'
})

const router = useRouter()
const route = useRoute()
const activeIndex = ref('1')

// 路由映射
const routeMap: Record<string, string> = {
  '1': 'dashboard',
  '2': 'terrain',
  '3': 'road-design',
  '4': 'route-optimization',
  '5': 'safety-detection',
  '6': 'cad-management',
  '7': 'data-analysis'
}

// 反向映射
const indexMap: Record<string, string> = Object.fromEntries(
  Object.entries(routeMap).map(([key, value]) => [value, key])
)

// 监听路由变化
watch(() => route.name, (newRouteName) => {
  if (newRouteName && indexMap[newRouteName as string]) {
    activeIndex.value = indexMap[newRouteName as string]
  }
}, { immediate: true })

const handleSelect = (key: string) => {
  activeIndex.value = key
  const routeName = routeMap[key]
  if (routeName) {
    router.push({ name: routeName })
  }
}
</script>

<style scoped>
.app-header {
  height: 60px;
  background: linear-gradient(135deg, #2c2c2c 0%, #1a1a1a 100%);
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
  border-bottom: 2px solid #ffd700;
}

.header-left {
  display: flex;
  align-items: center;
}

.logo {
  display: flex;
  align-items: center;
  color: #ffd700;
  font-size: 18px;
  font-weight: bold;
}

.logo-icon {
  font-size: 24px;
  margin-right: 8px;
}

.logo-text {
  color: #ffffff;
}

.header-center {
  flex: 1;
  display: flex;
  justify-content: center;
}

.header-menu {
  background: transparent;
  border: none;
}

.header-menu .el-menu-item {
  color: #cccccc;
  border-bottom: 2px solid transparent;
  transition: all 0.3s ease;
}

.header-menu .el-menu-item:hover {
  color: #ffd700;
  background: rgba(255, 215, 0, 0.1);
}

.header-menu .el-menu-item.is-active {
  color: #ffd700;
  border-bottom-color: #ffd700;
  background: rgba(255, 215, 0, 0.1);
}

.header-right {
  display: flex;
  align-items: center;
}

.header-right .el-button {
  background: #ffd700;
  border-color: #ffd700;
  color: #1a1a1a;
}

.header-right .el-button:hover {
  background: #ffed4e;
  border-color: #ffed4e;
}
</style>
