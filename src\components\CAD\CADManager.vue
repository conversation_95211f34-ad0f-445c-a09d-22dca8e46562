<template>
  <div class="cad-manager">
    <div class="manager-header">
      <div class="header-left">
        <h3>CAD数据管理</h3>
        <el-tag type="info" size="small">
          共 {{ cadDataList.length }} 个文件
        </el-tag>
      </div>
      <div class="header-right">
        <el-button type="primary" size="small" @click="showImportDialog">
          <el-icon><Plus /></el-icon>
          导入CAD
        </el-button>
        <el-button type="success" size="small" @click="exportSelected">
          <el-icon><Download /></el-icon>
          导出选中
        </el-button>
      </div>
    </div>

    <div class="manager-content">
      <!-- CAD文件列表 -->
      <div class="cad-list">
        <el-table 
          :data="cadDataList" 
          style="width: 100%" 
          size="small"
          @selection-change="handleSelectionChange"
          @row-click="handleRowClick"
        >
          <el-table-column type="selection" width="55" />
          <el-table-column prop="name" label="文件名" min-width="150">
            <template #default="{ row }">
              <div class="file-info">
                <el-icon class="file-icon">
                  <component :is="getFileIcon(row.format)" />
                </el-icon>
                <span>{{ row.name }}</span>
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="format" label="格式" width="80">
            <template #default="{ row }">
              <el-tag :type="getFormatTag(row.format)" size="small">
                {{ row.format.toUpperCase() }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column label="图层数" width="80">
            <template #default="{ row }">
              {{ row.layers.length }}
            </template>
          </el-table-column>
          <el-table-column label="实体数" width="80">
            <template #default="{ row }">
              {{ getTotalEntities(row) }}
            </template>
          </el-table-column>
          <el-table-column prop="uploadTime" label="导入时间" width="150">
            <template #default="{ row }">
              {{ formatDate(row.uploadTime) }}
            </template>
          </el-table-column>
          <el-table-column label="状态" width="80">
            <template #default="{ row }">
              <el-switch 
                v-model="row.visible" 
                @change="toggleVisibility(row)"
              />
            </template>
          </el-table-column>
          <el-table-column label="操作" width="200" fixed="right">
            <template #default="{ row }">
              <el-button type="primary" size="small" @click="viewCAD(row)">
                <el-icon><View /></el-icon>
                查看
              </el-button>
              <el-button type="info" size="small" @click="editCAD(row)">
                <el-icon><Edit /></el-icon>
                编辑
              </el-button>
              <el-button type="danger" size="small" @click="deleteCAD(row)">
                <el-icon><Delete /></el-icon>
                删除
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <!-- CAD预览区域 -->
      <div v-if="selectedCAD" class="cad-preview">
        <div class="preview-header">
          <h4>{{ selectedCAD.name }}</h4>
          <div class="preview-actions">
            <el-button type="primary" size="small" @click="openInViewer">
              <el-icon><FullScreen /></el-icon>
              全屏查看
            </el-button>
            <el-button type="success" size="small" @click="exportCAD">
              <el-icon><Download /></el-icon>
              导出
            </el-button>
          </div>
        </div>
        <div class="preview-content">
          <CADViewer 
            :cad-document="selectedCADDocument"
            :width="400"
            :height="300"
            @entity-selected="onEntitySelected"
            @layer-toggled="onLayerToggled"
          />
        </div>
        <div class="preview-info">
          <el-descriptions :column="2" size="small" border>
            <el-descriptions-item label="格式">
              {{ selectedCAD.format.toUpperCase() }}
            </el-descriptions-item>
            <el-descriptions-item label="图层数">
              {{ selectedCAD.layers.length }}
            </el-descriptions-item>
            <el-descriptions-item label="总实体数">
              {{ getTotalEntities(selectedCAD) }}
            </el-descriptions-item>
            <el-descriptions-item label="文件大小">
              {{ formatFileSize(selectedCAD.size || 0) }}
            </el-descriptions-item>
          </el-descriptions>
        </div>
      </div>
    </div>

    <!-- CAD导入对话框 -->
    <el-dialog 
      v-model="importDialogVisible" 
      title="CAD文件导入" 
      width="600px"
      :before-close="handleImportDialogClose"
    >
      <div class="import-content">
        <el-upload
          ref="uploadRef"
          class="upload-demo"
          drag
          :action="uploadAction"
          :before-upload="beforeUpload"
          :on-success="onUploadSuccess"
          :on-error="onUploadError"
          :file-list="fileList"
          multiple
          accept=".dwg,.dxf,.dgn"
        >
          <el-icon class="el-icon--upload"><UploadFilled /></el-icon>
          <div class="el-upload__text">
            将CAD文件拖到此处，或<em>点击上传</em>
          </div>
          <template #tip>
            <div class="el-upload__tip">
              支持格式：DWG, DXF, DGN，单个文件不超过50MB
            </div>
          </template>
        </el-upload>

        <div class="import-options">
          <el-form :model="importOptions" label-width="100px" size="small">
            <el-form-item label="坐标系统">
              <el-select v-model="importOptions.coordinateSystem" placeholder="选择坐标系统">
                <el-option label="WGS84" value="wgs84" />
                <el-option label="北京54" value="beijing54" />
                <el-option label="西安80" value="xian80" />
                <el-option label="CGCS2000" value="cgcs2000" />
              </el-select>
            </el-form-item>
            <el-form-item label="单位">
              <el-select v-model="importOptions.unit" placeholder="选择单位">
                <el-option label="毫米" value="millimeter" />
                <el-option label="米" value="meter" />
                <el-option label="英尺" value="foot" />
              </el-select>
            </el-form-item>
            <el-form-item label="比例">
              <el-input-number 
                v-model="importOptions.scale" 
                :min="0.001" 
                :max="1000" 
                :step="0.1" 
                :precision="3"
              />
            </el-form-item>
            <el-form-item label="自动可见">
              <el-switch v-model="importOptions.autoVisible" />
            </el-form-item>
          </el-form>
        </div>
      </div>
      
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="importDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="startImport" :loading="importing">
            开始导入
          </el-button>
        </span>
      </template>
    </el-dialog>

    <!-- CAD查看器对话框 -->
    <el-dialog 
      v-model="viewerDialogVisible" 
      :title="viewerTitle"
      width="90%"
      top="5vh"
      :before-close="handleViewerDialogClose"
    >
      <CADViewer 
        v-if="viewerCADDocument"
        :cad-document="viewerCADDocument"
        @entity-selected="onEntitySelected"
        @layer-toggled="onLayerToggled"
        @entity-modified="onEntityModified"
      />
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { 
  Plus, Download, View, Edit, Delete, FullScreen,
  UploadFilled, Files, Document 
} from '@element-plus/icons-vue'
import { useDataStore } from '@/stores/dataStore'
import { cadParser, type CADDocument, type CADEntity } from '@/utils/cadParser'
import CADViewer from './CADViewer.vue'

// 定义组件名称
defineOptions({
  name: 'CADManager'
})

const dataStore = useDataStore()

// 响应式数据
const selectedCAD = ref<any>(null)
const selectedCADDocument = ref<CADDocument | null>(null)
const selectedRows = ref<any[]>([])

// 对话框状态
const importDialogVisible = ref(false)
const viewerDialogVisible = ref(false)
const viewerTitle = ref('')
const viewerCADDocument = ref<CADDocument | null>(null)

// 导入相关
const uploadRef = ref()
const uploadAction = ref('#')
const fileList = ref([])
const importing = ref(false)

const importOptions = reactive({
  coordinateSystem: 'wgs84',
  unit: 'millimeter',
  scale: 1.0,
  autoVisible: true
})

// 计算属性
const cadDataList = computed(() => dataStore.cadDataList)

// 方法
const handleSelectionChange = (selection: any[]) => {
  selectedRows.value = selection
}

const handleRowClick = async (row: any) => {
  selectedCAD.value = row
  
  // 模拟加载CAD文档
  try {
    // 这里应该从实际数据中加载CAD文档
    selectedCADDocument.value = {
      fileName: row.name,
      format: row.format,
      version: '2018',
      units: 'millimeters',
      bounds: { minX: 0, minY: 0, maxX: 1000, maxY: 1000 },
      layers: row.layers,
      blocks: [],
      metadata: {}
    }
  } catch (error) {
    console.error('加载CAD文档失败:', error)
    ElMessage.error('加载CAD文档失败')
  }
}

const showImportDialog = () => {
  importDialogVisible.value = true
}

const handleImportDialogClose = () => {
  importDialogVisible.value = false
  fileList.value = []
}

const beforeUpload = (file: File) => {
  const validTypes = ['dwg', 'dxf', 'dgn']
  const fileType = file.name.split('.').pop()?.toLowerCase()
  
  if (!validTypes.includes(fileType || '')) {
    ElMessage.error('不支持的文件格式！')
    return false
  }
  
  if (file.size > 50 * 1024 * 1024) { // 50MB
    ElMessage.error('文件大小不能超过50MB！')
    return false
  }
  
  return true
}

const onUploadSuccess = (response: any, file: any) => {
  console.log('Upload success:', response, file)
}

const onUploadError = (error: any) => {
  console.error('Upload error:', error)
  ElMessage.error('文件上传失败！')
}

const startImport = async () => {
  if (fileList.value.length === 0) {
    ElMessage.warning('请先选择要导入的文件')
    return
  }

  importing.value = true
  
  try {
    for (const fileItem of fileList.value) {
      const file = (fileItem as any).raw
      if (!file) continue
      
      const format = file.name.split('.').pop()?.toLowerCase() as 'dwg' | 'dxf' | 'dgn'
      
      // 解析CAD文件
      const cadDocument = await cadParser.parseCADFile(file, format)
      
      // 添加到数据存储
      const cadData = {
        name: file.name,
        format,
        layers: cadDocument.layers.map(layer => ({
          name: layer.name,
          visible: layer.visible,
          color: layer.color,
          entities: layer.entities
        })),
        visible: importOptions.autoVisible,
        size: file.size
      }
      
      dataStore.addCADData(cadData)
    }
    
    ElMessage.success('CAD文件导入成功！')
    importDialogVisible.value = false
    fileList.value = []
  } catch (error) {
    console.error('导入失败:', error)
    ElMessage.error('CAD文件导入失败！')
  } finally {
    importing.value = false
  }
}

const viewCAD = (row: any) => {
  viewerTitle.value = `查看 - ${row.name}`
  viewerCADDocument.value = {
    fileName: row.name,
    format: row.format,
    version: '2018',
    units: 'millimeters',
    bounds: { minX: 0, minY: 0, maxX: 1000, maxY: 1000 },
    layers: row.layers,
    blocks: [],
    metadata: {}
  }
  viewerDialogVisible.value = true
}

const editCAD = (row: any) => {
  ElMessage.info('CAD编辑功能开发中...')
}

const deleteCAD = async (row: any) => {
  try {
    await ElMessageBox.confirm('确定要删除这个CAD文件吗？', '确认删除', {
      type: 'warning'
    })
    
    dataStore.removeCADData(row.id)
    
    if (selectedCAD.value?.id === row.id) {
      selectedCAD.value = null
      selectedCADDocument.value = null
    }
    
    ElMessage.success('删除成功！')
  } catch {
    // 用户取消删除
  }
}

const toggleVisibility = (row: any) => {
  dataStore.updateCADData(row.id, { visible: row.visible })
}

const exportSelected = () => {
  if (selectedRows.value.length === 0) {
    ElMessage.warning('请先选择要导出的文件')
    return
  }
  
  ElMessage.info('批量导出功能开发中...')
}

const openInViewer = () => {
  if (selectedCAD.value) {
    viewCAD(selectedCAD.value)
  }
}

const exportCAD = () => {
  if (selectedCAD.value) {
    ElMessage.info('CAD导出功能开发中...')
  }
}

const handleViewerDialogClose = () => {
  viewerDialogVisible.value = false
  viewerCADDocument.value = null
}

// 事件处理
const onEntitySelected = (entity: CADEntity) => {
  console.log('Entity selected:', entity)
}

const onLayerToggled = (layerName: string, visible: boolean) => {
  console.log('Layer toggled:', layerName, visible)
}

const onEntityModified = (entity: CADEntity) => {
  console.log('Entity modified:', entity)
  // 更新数据存储中的实体
}

// 工具函数
const getFileIcon = (format: string) => {
  const iconMap: Record<string, string> = {
    dwg: 'Files',
    dxf: 'Document',
    dgn: 'Files'
  }
  return iconMap[format] || 'Files'
}

const getFormatTag = (format: string) => {
  const tagMap: Record<string, string> = {
    dwg: 'primary',
    dxf: 'success',
    dgn: 'warning'
  }
  return tagMap[format] || 'info'
}

const getTotalEntities = (cadData: any) => {
  return cadData.layers.reduce((total: number, layer: any) => {
    return total + (layer.entities?.length || 0)
  }, 0)
}

const formatDate = (date: Date) => {
  return new Date(date).toLocaleString('zh-CN')
}

const formatFileSize = (bytes: number) => {
  if (bytes === 0) return '0 B'
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}
</script>

<style scoped>
.cad-manager {
  height: 100%;
  display: flex;
  flex-direction: column;
  background: #1a1a1a;
}

.manager-header {
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 20px;
  background: #2a2a2a;
  border-bottom: 1px solid #404040;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 15px;
}

.header-left h3 {
  color: #ffd700;
  margin: 0;
  font-size: 18px;
}

.header-right {
  display: flex;
  gap: 10px;
}

.manager-content {
  flex: 1;
  display: flex;
  overflow: hidden;
}

.cad-list {
  flex: 1;
  padding: 15px;
  overflow: auto;
}

.file-info {
  display: flex;
  align-items: center;
  gap: 8px;
}

.file-icon {
  color: #ffd700;
  font-size: 16px;
}

.cad-preview {
  width: 450px;
  background: #2a2a2a;
  border-left: 1px solid #404040;
  display: flex;
  flex-direction: column;
}

.preview-header {
  height: 50px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 15px;
  background: #333333;
  border-bottom: 1px solid #404040;
}

.preview-header h4 {
  color: #ffd700;
  margin: 0;
  font-size: 14px;
}

.preview-actions {
  display: flex;
  gap: 8px;
}

.preview-content {
  flex: 1;
  padding: 15px;
  overflow: hidden;
}

.preview-info {
  padding: 15px;
  border-top: 1px solid #404040;
}

.import-content {
  padding: 20px 0;
}

.upload-demo {
  margin-bottom: 20px;
}

.import-options {
  background: #1a1a1a;
  padding: 15px;
  border-radius: 6px;
  border: 1px solid #404040;
}

/* Element Plus 组件样式定制 */
:deep(.el-table) {
  background: #1a1a1a;
  color: #cccccc;
}

:deep(.el-table th) {
  background: #2a2a2a;
  color: #ffd700;
  border-color: #404040;
}

:deep(.el-table td) {
  border-color: #404040;
}

:deep(.el-upload-dragger) {
  background: #1a1a1a;
  border-color: #404040;
  color: #cccccc;
}

:deep(.el-upload-dragger:hover) {
  border-color: #ffd700;
}

:deep(.el-descriptions__label) {
  color: #ffd700;
}

:deep(.el-descriptions__content) {
  color: #cccccc;
}
</style>
