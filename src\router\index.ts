import { createRouter, createWebHistory } from 'vue-router'
import AppLayout from '../components/Layout/AppLayout.vue'

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes: [
    {
      path: '/',
      name: 'home',
      component: AppLayout,
      children: [
        {
          path: '',
          name: 'dashboard',
          component: () => import('../views/DashboardView.vue')
        },
        {
          path: 'terrain',
          name: 'terrain',
          component: () => import('../views/DashboardView.vue')
        },
        {
          path: 'road-design',
          name: 'road-design',
          component: () => import('../views/DashboardView.vue')
        },
        {
          path: 'route-optimization',
          name: 'route-optimization',
          component: () => import('../views/DashboardView.vue')
        },
        {
          path: 'safety-detection',
          name: 'safety-detection',
          component: () => import('../views/DashboardView.vue')
        },
        {
          path: 'cad-management',
          name: 'cad-management',
          component: () => import('../views/CADManagementView.vue')
        },
        {
          path: 'data-analysis',
          name: 'data-analysis',
          component: () => import('../views/DashboardView.vue')
        }
      ]
    }
  ],
})

export default router
