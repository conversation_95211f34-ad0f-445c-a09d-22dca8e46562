<template>
  <div class="data-import">
    <el-tabs v-model="activeTab" type="border-card" class="import-tabs">
      <!-- 地形数据导入 -->
      <el-tab-pane label="地形数据" name="terrain">
        <div class="import-section">
          <el-upload
            ref="terrainUploadRef"
            class="upload-demo"
            drag
            :action="uploadAction"
            :before-upload="beforeTerrainUpload"
            :on-success="onTerrainUploadSuccess"
            :on-error="onUploadError"
            :file-list="terrainFileList"
            multiple
            accept=".tif,.tiff,.dem,.asc,.xyz,.las,.laz"
          >
            <el-icon class="el-icon--upload"><UploadFilled /></el-icon>
            <div class="el-upload__text">
              将地形文件拖到此处，或<em>点击上传</em>
            </div>
            <template #tip>
              <div class="el-upload__tip">
                支持格式：TIFF, DEM, ASCII Grid, XYZ, LAS, LAZ 等
              </div>
            </template>
          </el-upload>
          
          <div class="import-options">
            <el-form :model="terrainOptions" label-width="100px" size="small">
              <el-form-item label="数据类型">
                <el-select v-model="terrainOptions.type" placeholder="选择数据类型">
                  <el-option label="DEM高程" value="dem" />
                  <el-option label="等高线" value="contour" />
                  <el-option label="点云数据" value="point_cloud" />
                </el-select>
              </el-form-item>
              <el-form-item label="透明度">
                <el-slider v-model="terrainOptions.opacity" :min="0" :max="100" />
              </el-form-item>
              <el-form-item label="默认可见">
                <el-switch v-model="terrainOptions.visible" />
              </el-form-item>
            </el-form>
          </div>
        </div>
      </el-tab-pane>

      <!-- 钻孔数据导入 -->
      <el-tab-pane label="钻孔数据" name="drillhole">
        <div class="import-section">
          <el-upload
            ref="drillholeUploadRef"
            class="upload-demo"
            drag
            :action="uploadAction"
            :before-upload="beforeDrillholeUpload"
            :on-success="onDrillholeUploadSuccess"
            :on-error="onUploadError"
            :file-list="drillholeFileList"
            multiple
            accept=".csv,.xlsx,.txt,.json"
          >
            <el-icon class="el-icon--upload"><UploadFilled /></el-icon>
            <div class="el-upload__text">
              将钻孔文件拖到此处，或<em>点击上传</em>
            </div>
            <template #tip>
              <div class="el-upload__tip">
                支持格式：CSV, Excel, TXT, JSON 等
              </div>
            </template>
          </el-upload>

          <div class="import-options">
            <el-form :model="drillholeOptions" label-width="100px" size="small">
              <el-form-item label="坐标系统">
                <el-select v-model="drillholeOptions.coordinateSystem" placeholder="选择坐标系统">
                  <el-option label="WGS84" value="wgs84" />
                  <el-option label="北京54" value="beijing54" />
                  <el-option label="西安80" value="xian80" />
                  <el-option label="CGCS2000" value="cgcs2000" />
                </el-select>
              </el-form-item>
              <el-form-item label="数据编码">
                <el-select v-model="drillholeOptions.encoding" placeholder="选择编码">
                  <el-option label="UTF-8" value="utf-8" />
                  <el-option label="GBK" value="gbk" />
                  <el-option label="GB2312" value="gb2312" />
                </el-select>
              </el-form-item>
            </el-form>
          </div>
        </div>
      </el-tab-pane>

      <!-- 地质数据导入 -->
      <el-tab-pane label="地质数据" name="geology">
        <div class="import-section">
          <el-upload
            ref="geologyUploadRef"
            class="upload-demo"
            drag
            :action="uploadAction"
            :before-upload="beforeGeologyUpload"
            :on-success="onGeologyUploadSuccess"
            :on-error="onUploadError"
            :file-list="geologyFileList"
            multiple
            accept=".shp,.kml,.kmz,.geojson,.gml"
          >
            <el-icon class="el-icon--upload"><UploadFilled /></el-icon>
            <div class="el-upload__text">
              将地质文件拖到此处，或<em>点击上传</em>
            </div>
            <template #tip>
              <div class="el-upload__tip">
                支持格式：Shapefile, KML, GeoJSON, GML 等
              </div>
            </template>
          </el-upload>

          <div class="import-options">
            <el-form :model="geologyOptions" label-width="100px" size="small">
              <el-form-item label="地质类型">
                <el-select v-model="geologyOptions.type" placeholder="选择地质类型">
                  <el-option label="地层" value="stratum" />
                  <el-option label="断层" value="fault" />
                  <el-option label="矿体" value="ore_body" />
                </el-select>
              </el-form-item>
              <el-form-item label="显示颜色">
                <el-color-picker v-model="geologyOptions.color" />
              </el-form-item>
              <el-form-item label="填充颜色">
                <el-color-picker v-model="geologyOptions.fillColor" />
              </el-form-item>
            </el-form>
          </div>
        </div>
      </el-tab-pane>

      <!-- CAD数据导入 -->
      <el-tab-pane label="CAD数据" name="cad">
        <div class="import-section">
          <el-upload
            ref="cadUploadRef"
            class="upload-demo"
            drag
            :action="uploadAction"
            :before-upload="beforeCADUpload"
            :on-success="onCADUploadSuccess"
            :on-error="onUploadError"
            :file-list="cadFileList"
            multiple
            accept=".dwg,.dxf,.dgn"
          >
            <el-icon class="el-icon--upload"><UploadFilled /></el-icon>
            <div class="el-upload__text">
              将CAD文件拖到此处，或<em>点击上传</em>
            </div>
            <template #tip>
              <div class="el-upload__tip">
                支持格式：DWG, DXF, DGN 等
              </div>
            </template>
          </el-upload>

          <div class="import-options">
            <el-form :model="cadOptions" label-width="100px" size="small">
              <el-form-item label="单位">
                <el-select v-model="cadOptions.unit" placeholder="选择单位">
                  <el-option label="米" value="meter" />
                  <el-option label="毫米" value="millimeter" />
                  <el-option label="英尺" value="foot" />
                </el-select>
              </el-form-item>
              <el-form-item label="比例">
                <el-input-number v-model="cadOptions.scale" :min="0.1" :max="1000" :step="0.1" />
              </el-form-item>
            </el-form>
          </div>
        </div>
      </el-tab-pane>
    </el-tabs>

    <!-- 导入进度 -->
    <div v-if="importing" class="import-progress">
      <el-progress :percentage="importProgress" :status="importStatus" />
      <p>{{ importMessage }}</p>
    </div>

    <!-- 操作按钮 -->
    <div class="import-actions">
      <el-button @click="clearAll" :disabled="importing">清空所有</el-button>
      <el-button type="primary" @click="startImport" :loading="importing">
        开始导入
      </el-button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import { ElMessage } from 'element-plus'
import { UploadFilled } from '@element-plus/icons-vue'
import { useDataStore } from '@/stores/dataStore'

const dataStore = useDataStore()

// 当前活动标签
const activeTab = ref('terrain')

// 上传相关
const uploadAction = ref('#') // 实际项目中应该是真实的上传接口
const importing = ref(false)
const importProgress = ref(0)
const importStatus = ref<'success' | 'exception' | 'warning' | ''>('')
const importMessage = ref('')

// 文件列表
const terrainFileList = ref([])
const drillholeFileList = ref([])
const geologyFileList = ref([])
const cadFileList = ref([])

// 导入选项
const terrainOptions = reactive({
  type: 'dem',
  opacity: 80,
  visible: true
})

const drillholeOptions = reactive({
  coordinateSystem: 'wgs84',
  encoding: 'utf-8'
})

const geologyOptions = reactive({
  type: 'stratum',
  color: '#ff0000',
  fillColor: '#ff000080'
})

const cadOptions = reactive({
  unit: 'meter',
  scale: 1.0
})

// 上传前检查
const beforeTerrainUpload = (file: File) => {
  const validTypes = ['tif', 'tiff', 'dem', 'asc', 'xyz', 'las', 'laz']
  const fileType = file.name.split('.').pop()?.toLowerCase()
  
  if (!validTypes.includes(fileType || '')) {
    ElMessage.error('不支持的文件格式！')
    return false
  }
  
  if (file.size > 100 * 1024 * 1024) { // 100MB
    ElMessage.error('文件大小不能超过100MB！')
    return false
  }
  
  return true
}

const beforeDrillholeUpload = (file: File) => {
  const validTypes = ['csv', 'xlsx', 'txt', 'json']
  const fileType = file.name.split('.').pop()?.toLowerCase()
  
  if (!validTypes.includes(fileType || '')) {
    ElMessage.error('不支持的文件格式！')
    return false
  }
  
  return true
}

const beforeGeologyUpload = (file: File) => {
  const validTypes = ['shp', 'kml', 'kmz', 'geojson', 'gml']
  const fileType = file.name.split('.').pop()?.toLowerCase()
  
  if (!validTypes.includes(fileType || '')) {
    ElMessage.error('不支持的文件格式！')
    return false
  }
  
  return true
}

const beforeCADUpload = (file: File) => {
  const validTypes = ['dwg', 'dxf', 'dgn']
  const fileType = file.name.split('.').pop()?.toLowerCase()
  
  if (!validTypes.includes(fileType || '')) {
    ElMessage.error('不支持的文件格式！')
    return false
  }
  
  return true
}

// 上传成功回调
const onTerrainUploadSuccess = (response: any, file: any) => {
  console.log('Terrain upload success:', response, file)
  ElMessage.success('地形数据上传成功！')
}

const onDrillholeUploadSuccess = (response: any, file: any) => {
  console.log('Drillhole upload success:', response, file)
  ElMessage.success('钻孔数据上传成功！')
}

const onGeologyUploadSuccess = (response: any, file: any) => {
  console.log('Geology upload success:', response, file)
  ElMessage.success('地质数据上传成功！')
}

const onCADUploadSuccess = (response: any, file: any) => {
  console.log('CAD upload success:', response, file)
  ElMessage.success('CAD数据上传成功！')
}

const onUploadError = (error: any) => {
  console.error('Upload error:', error)
  ElMessage.error('文件上传失败！')
}

// 开始导入
const startImport = async () => {
  importing.value = true
  importProgress.value = 0
  importStatus.value = ''
  importMessage.value = '正在处理文件...'

  try {
    // 模拟导入过程
    for (let i = 0; i <= 100; i += 10) {
      await new Promise(resolve => setTimeout(resolve, 200))
      importProgress.value = i
      
      if (i === 30) importMessage.value = '解析文件格式...'
      if (i === 60) importMessage.value = '验证数据完整性...'
      if (i === 90) importMessage.value = '保存到数据库...'
    }

    importStatus.value = 'success'
    importMessage.value = '导入完成！'
    ElMessage.success('数据导入成功！')
  } catch (error) {
    importStatus.value = 'exception'
    importMessage.value = '导入失败！'
    ElMessage.error('数据导入失败！')
  } finally {
    importing.value = false
  }
}

// 清空所有
const clearAll = () => {
  terrainFileList.value = []
  drillholeFileList.value = []
  geologyFileList.value = []
  cadFileList.value = []
  ElMessage.info('已清空所有文件')
}
</script>

<style scoped>
.data-import {
  padding: 20px;
}

.import-tabs {
  background: #2a2a2a;
  border-color: #404040;
}

.import-section {
  padding: 20px 0;
}

.upload-demo {
  margin-bottom: 20px;
}

.import-options {
  background: #1a1a1a;
  padding: 15px;
  border-radius: 6px;
  border: 1px solid #404040;
}

.import-progress {
  margin: 20px 0;
  padding: 15px;
  background: #1a1a1a;
  border-radius: 6px;
  border: 1px solid #404040;
}

.import-actions {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
  margin-top: 20px;
}

/* Element Plus 组件样式定制 */
:deep(.el-upload-dragger) {
  background: #1a1a1a;
  border-color: #404040;
  color: #cccccc;
}

:deep(.el-upload-dragger:hover) {
  border-color: #ffd700;
}

:deep(.el-upload__tip) {
  color: #999999;
}

:deep(.el-tabs__item) {
  color: #cccccc;
}

:deep(.el-tabs__item.is-active) {
  color: #ffd700;
}

:deep(.el-tabs__nav-wrap::after) {
  background: #404040;
}

:deep(.el-tabs__active-bar) {
  background: #ffd700;
}
</style>
