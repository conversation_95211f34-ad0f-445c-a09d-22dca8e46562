<template>
  <div class="dashboard-view">
    <div class="dashboard-header">
      <h2>露天矿道路设计系统</h2>
      <p>欢迎使用专业的露天矿山道路设计与优化平台</p>
    </div>
    
    <div class="dashboard-content">
      <!-- 统计卡片 -->
      <div class="stats-cards">
        <el-row :gutter="20">
          <el-col :span="6">
            <el-card class="stat-card">
              <div class="stat-content">
                <div class="stat-icon terrain">
                  <el-icon><MapLocation /></el-icon>
                </div>
                <div class="stat-info">
                  <h3>{{ terrainDataCount }}</h3>
                  <p>地形数据</p>
                </div>
              </div>
            </el-card>
          </el-col>
          <el-col :span="6">
            <el-card class="stat-card">
              <div class="stat-content">
                <div class="stat-icon drillhole">
                  <el-icon><Position /></el-icon>
                </div>
                <div class="stat-info">
                  <h3>{{ drillHoleDataCount }}</h3>
                  <p>钻孔数据</p>
                </div>
              </div>
            </el-card>
          </el-col>
          <el-col :span="6">
            <el-card class="stat-card">
              <div class="stat-content">
                <div class="stat-icon geology">
                  <el-icon><Grid /></el-icon>
                </div>
                <div class="stat-info">
                  <h3>{{ geologyDataCount }}</h3>
                  <p>地质数据</p>
                </div>
              </div>
            </el-card>
          </el-col>
          <el-col :span="6">
            <el-card class="stat-card">
              <div class="stat-content">
                <div class="stat-icon cad">
                  <el-icon><Files /></el-icon>
                </div>
                <div class="stat-info">
                  <h3>{{ cadDataCount }}</h3>
                  <p>CAD文件</p>
                </div>
              </div>
            </el-card>
          </el-col>
        </el-row>
      </div>

      <!-- 功能模块 -->
      <div class="function-modules">
        <h3>功能模块</h3>
        <el-row :gutter="20">
          <el-col :span="8">
            <el-card class="module-card" @click="navigateTo('terrain')">
              <div class="module-content">
                <div class="module-icon">
                  <el-icon><MapLocation /></el-icon>
                </div>
                <h4>地形数据管理</h4>
                <p>导入、处理和管理DEM、等高线等地形数据</p>
              </div>
            </el-card>
          </el-col>
          <el-col :span="8">
            <el-card class="module-card" @click="navigateTo('road-design')">
              <div class="module-content">
                <div class="module-icon">
                  <el-icon><Connection /></el-icon>
                </div>
                <h4>道路设计</h4>
                <p>进行道路选线、设计和冲突检测</p>
              </div>
            </el-card>
          </el-col>
          <el-col :span="8">
            <el-card class="module-card" @click="navigateTo('route-optimization')">
              <div class="module-content">
                <div class="module-icon">
                  <el-icon><TrendCharts /></el-icon>
                </div>
                <h4>路线优化</h4>
                <p>优化运输路线，提高运输效率</p>
              </div>
            </el-card>
          </el-col>
        </el-row>
        <el-row :gutter="20" style="margin-top: 20px;">
          <el-col :span="8">
            <el-card class="module-card" @click="navigateTo('safety-detection')">
              <div class="module-content">
                <div class="module-icon">
                  <el-icon><Warning /></el-icon>
                </div>
                <h4>安全检测</h4>
                <p>检测道路安全参数，确保设计合规</p>
              </div>
            </el-card>
          </el-col>
          <el-col :span="8">
            <el-card class="module-card" @click="navigateTo('cad-management')">
              <div class="module-content">
                <div class="module-icon">
                  <el-icon><Files /></el-icon>
                </div>
                <h4>CAD数据管理</h4>
                <p>导入、查看和编辑CAD设计文件</p>
              </div>
            </el-card>
          </el-col>
          <el-col :span="8">
            <el-card class="module-card" @click="navigateTo('data-analysis')">
              <div class="module-content">
                <div class="module-icon">
                  <el-icon><DataAnalysis /></el-icon>
                </div>
                <h4>数据分析</h4>
                <p>分析设计数据，生成报告和图表</p>
              </div>
            </el-card>
          </el-col>
        </el-row>
      </div>

      <!-- 最近活动 -->
      <div class="recent-activities">
        <h3>最近活动</h3>
        <el-timeline>
          <el-timeline-item
            v-for="activity in recentActivities"
            :key="activity.id"
            :timestamp="activity.timestamp"
            :type="activity.type"
          >
            <div class="activity-content">
              <h4>{{ activity.title }}</h4>
              <p>{{ activity.description }}</p>
            </div>
          </el-timeline-item>
        </el-timeline>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, ref } from 'vue'
import { useRouter } from 'vue-router'
import { 
  MapLocation, Position, Grid, Files, Connection, 
  TrendCharts, Warning, DataAnalysis 
} from '@element-plus/icons-vue'
import { useDataStore } from '@/stores/dataStore'

// 定义组件名称
defineOptions({
  name: 'DashboardView'
})

const router = useRouter()
const dataStore = useDataStore()

// 计算属性
const terrainDataCount = computed(() => dataStore.terrainDataList.length)
const drillHoleDataCount = computed(() => dataStore.drillHoleDataList.length)
const geologyDataCount = computed(() => dataStore.geologyDataList.length)
const cadDataCount = computed(() => dataStore.cadDataList.length)

// 最近活动数据
const recentActivities = ref([
  {
    id: 1,
    title: '导入地形数据',
    description: '成功导入DEM高程数据文件 terrain_001.tif',
    timestamp: '2024-01-20 14:30',
    type: 'primary'
  },
  {
    id: 2,
    title: '完成道路设计',
    description: '完成主干道路线设计，总长度1.2公里',
    timestamp: '2024-01-20 11:15',
    type: 'success'
  },
  {
    id: 3,
    title: '安全检测警告',
    description: '检测到转弯半径过小，建议调整设计',
    timestamp: '2024-01-20 09:45',
    type: 'warning'
  },
  {
    id: 4,
    title: '导入CAD文件',
    description: '导入AutoCAD设计文件 road_design.dwg',
    timestamp: '2024-01-19 16:20',
    type: 'info'
  }
])

// 方法
const navigateTo = (routeName: string) => {
  router.push({ name: routeName })
}
</script>

<style scoped>
.dashboard-view {
  padding: 20px;
  height: 100%;
  overflow-y: auto;
}

.dashboard-header {
  text-align: center;
  margin-bottom: 30px;
}

.dashboard-header h2 {
  color: #ffd700;
  font-size: 28px;
  margin-bottom: 10px;
}

.dashboard-header p {
  color: #cccccc;
  font-size: 16px;
}

.dashboard-content {
  max-width: 1200px;
  margin: 0 auto;
}

.stats-cards {
  margin-bottom: 40px;
}

.stat-card {
  background: #2a2a2a;
  border-color: #404040;
  cursor: pointer;
  transition: all 0.3s ease;
}

.stat-card:hover {
  border-color: #ffd700;
  transform: translateY(-2px);
}

.stat-content {
  display: flex;
  align-items: center;
  gap: 15px;
}

.stat-icon {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  color: #ffffff;
}

.stat-icon.terrain {
  background: linear-gradient(135deg, #4CAF50, #45a049);
}

.stat-icon.drillhole {
  background: linear-gradient(135deg, #2196F3, #1976D2);
}

.stat-icon.geology {
  background: linear-gradient(135deg, #FF9800, #F57C00);
}

.stat-icon.cad {
  background: linear-gradient(135deg, #9C27B0, #7B1FA2);
}

.stat-info h3 {
  color: #ffd700;
  font-size: 24px;
  margin: 0 0 5px 0;
}

.stat-info p {
  color: #cccccc;
  margin: 0;
  font-size: 14px;
}

.function-modules {
  margin-bottom: 40px;
}

.function-modules h3 {
  color: #ffd700;
  margin-bottom: 20px;
  font-size: 20px;
}

.module-card {
  background: #2a2a2a;
  border-color: #404040;
  cursor: pointer;
  transition: all 0.3s ease;
  height: 180px;
}

.module-card:hover {
  border-color: #ffd700;
  transform: translateY(-3px);
  box-shadow: 0 8px 25px rgba(255, 215, 0, 0.2);
}

.module-content {
  text-align: center;
  padding: 20px;
}

.module-icon {
  width: 80px;
  height: 80px;
  margin: 0 auto 15px;
  background: linear-gradient(135deg, #ffd700, #ffed4e);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32px;
  color: #1a1a1a;
}

.module-content h4 {
  color: #ffd700;
  margin: 0 0 10px 0;
  font-size: 16px;
}

.module-content p {
  color: #cccccc;
  margin: 0;
  font-size: 12px;
  line-height: 1.4;
}

.recent-activities h3 {
  color: #ffd700;
  margin-bottom: 20px;
  font-size: 20px;
}

.activity-content h4 {
  color: #ffd700;
  margin: 0 0 5px 0;
  font-size: 14px;
}

.activity-content p {
  color: #cccccc;
  margin: 0;
  font-size: 12px;
}

/* Element Plus 组件样式定制 */
:deep(.el-card__body) {
  padding: 20px;
}

:deep(.el-timeline-item__timestamp) {
  color: #999999;
  font-size: 12px;
}

:deep(.el-timeline-item__node) {
  background-color: #ffd700;
  border-color: #ffd700;
}

:deep(.el-timeline-item__tail) {
  border-left-color: #404040;
}

/* 滚动条样式 */
.dashboard-view::-webkit-scrollbar {
  width: 8px;
}

.dashboard-view::-webkit-scrollbar-track {
  background: #1a1a1a;
}

.dashboard-view::-webkit-scrollbar-thumb {
  background: #404040;
  border-radius: 4px;
}

.dashboard-view::-webkit-scrollbar-thumb:hover {
  background: #555555;
}
</style>
