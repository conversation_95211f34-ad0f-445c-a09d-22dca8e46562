<template>
  <div id="cesium-container" ref="cesiumContainer" class="cesium-viewer"></div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'
import * as Cesium from 'cesium'

// 定义组件名称
defineOptions({
  name: 'CesiumViewer'
})

const cesiumContainer = ref<HTMLElement>()
let viewer: Cesium.Viewer | null = null

// 定义props
interface Props {
  terrainProvider?: string
  imageryProvider?: string
  homeView?: {
    longitude: number
    latitude: number
    height: number
  }
}

const props = withDefaults(defineProps<Props>(), {
  terrainProvider: 'world',
  imageryProvider: 'bing',
  homeView: () => ({
    longitude: 116.4074, // 北京经度
    latitude: 39.9042,   // 北京纬度
    height: 15000        // 高度
  })
})

// 定义emits
const emit = defineEmits<{
  viewerReady: [viewer: Cesium.Viewer]
  viewChanged: [view: any]
  entitySelected: [entity: Cesium.Entity]
}>()

// 初始化Cesium
const initCesium = () => {
  if (!cesiumContainer.value) return

  try {
    // 设置Cesium的静态资源路径
    ;(window as any).CESIUM_BASE_URL = '/cesium/'

    // 创建Cesium Viewer
    viewer = new Cesium.Viewer(cesiumContainer.value, {
      // 界面配置
      animation: false,           // 动画控件
      baseLayerPicker: true,      // 底图选择器
      fullscreenButton: true,     // 全屏按钮
      geocoder: true,             // 地名搜索
      homeButton: true,           // 主页按钮
      infoBox: true,              // 信息框
      sceneModePicker: true,      // 场景模式选择器
      selectionIndicator: true,   // 选择指示器
      timeline: false,            // 时间轴
      navigationHelpButton: true, // 导航帮助按钮
      navigationInstructionsInitiallyVisible: false,
      // 场景配置
      scene3DOnly: false,         // 允许2D/3D切换
      // 其他配置
      requestRenderMode: true,    // 按需渲染
      maximumRenderTimeChange: Infinity
    })

    // 设置初始视图
    setHomeView()

    // 配置场景
    configureScene()

    // 绑定事件
    bindEvents()

    // 发出viewer准备就绪事件
    emit('viewerReady', viewer)

    console.log('Cesium viewer initialized successfully')
  } catch (error) {
    console.error('Failed to initialize Cesium viewer:', error)
  }
}

// 创建地形提供者
const createTerrainProvider = () => {
  switch (props.terrainProvider) {
    case 'cesium':
      return Cesium.createWorldTerrainAsync()
    case 'ellipsoid':
      return new Cesium.EllipsoidTerrainProvider()
    default:
      return new Cesium.EllipsoidTerrainProvider()
  }
}

// 创建影像提供者
const createImageryProvider = () => {
  try {
    switch (props.imageryProvider) {
      case 'bing':
        return new Cesium.BingMapsImageryProvider({
          key: 'your-bing-maps-key', // 需要替换为实际的Bing Maps密钥
          mapStyle: Cesium.BingMapsStyle.AERIAL_WITH_LABELS
        })
      case 'osm':
        return new Cesium.OpenStreetMapImageryProvider({})
      case 'arcgis':
        return new Cesium.ArcGisMapServerImageryProvider({})
      default:
        return new Cesium.OpenStreetMapImageryProvider({})
    }
  } catch (error) {
    console.warn('Failed to create imagery provider, using default:', error)
    return new Cesium.OpenStreetMapImageryProvider({})
  }
}

// 设置主页视图
const setHomeView = () => {
  if (!viewer) return

  const { longitude, latitude, height } = props.homeView

  viewer.camera.setView({
    destination: Cesium.Cartesian3.fromDegrees(longitude, latitude, height),
    orientation: {
      heading: Cesium.Math.toRadians(0),
      pitch: Cesium.Math.toRadians(-45),
      roll: 0.0
    }
  })

  // 设置home按钮的默认视图
  viewer.homeButton.viewModel.command.beforeExecute.addEventListener((e) => {
    e.cancel = true
    if (viewer) {
      viewer.camera.setView({
        destination: Cesium.Cartesian3.fromDegrees(longitude, latitude, height),
        orientation: {
          heading: Cesium.Math.toRadians(0),
          pitch: Cesium.Math.toRadians(-45),
          roll: 0.0
        }
      })
    }
  })
}

// 配置场景
const configureScene = () => {
  if (!viewer) return

  const scene = viewer.scene
  
  // 启用深度测试
  scene.globe.depthTestAgainstTerrain = true
  
  // 设置大气效果
  scene.skyAtmosphere.show = true
  
  // 设置雾效
  scene.fog.enabled = true
  scene.fog.density = 0.0001
  
  // 设置光照
  scene.globe.enableLighting = true
  
  // 设置地球表面材质
  scene.globe.material = Cesium.Material.fromType('Color')
  scene.globe.material.uniforms.color = new Cesium.Color(0.1, 0.1, 0.1, 1.0)
}

// 绑定事件
const bindEvents = () => {
  if (!viewer) return

  // 相机移动事件
  viewer.camera.moveEnd.addEventListener(() => {
    if (!viewer) return

    const position = viewer.camera.position
    const cartographic = Cesium.Cartographic.fromCartesian(position)
    const longitude = Cesium.Math.toDegrees(cartographic.longitude)
    const latitude = Cesium.Math.toDegrees(cartographic.latitude)
    const height = cartographic.height

    emit('viewChanged', {
      longitude,
      latitude,
      height,
      heading: Cesium.Math.toDegrees(viewer.camera.heading),
      pitch: Cesium.Math.toDegrees(viewer.camera.pitch),
      roll: Cesium.Math.toDegrees(viewer.camera.roll)
    })
  })

  // 实体选择事件
  viewer.selectedEntityChanged.addEventListener((selectedEntity) => {
    if (selectedEntity) {
      emit('entitySelected', selectedEntity)
    }
  })
}

// 暴露方法给父组件
const getViewer = () => viewer

const flyTo = (longitude: number, latitude: number, height: number = 15000) => {
  if (!viewer) return
  
  viewer.camera.flyTo({
    destination: Cesium.Cartesian3.fromDegrees(longitude, latitude, height),
    duration: 2.0
  })
}

const setView2D = () => {
  if (!viewer) return
  viewer.scene.mode = Cesium.SceneMode.SCENE2D
}

const setView3D = () => {
  if (!viewer) return
  viewer.scene.mode = Cesium.SceneMode.SCENE3D
}

const setViewColumbus = () => {
  if (!viewer) return
  viewer.scene.mode = Cesium.SceneMode.COLUMBUS_VIEW
}

// 暴露方法
defineExpose({
  getViewer,
  flyTo,
  setView2D,
  setView3D,
  setViewColumbus
})

onMounted(() => {
  initCesium()
})

onUnmounted(() => {
  if (viewer) {
    viewer.destroy()
    viewer = null
  }
})
</script>

<style scoped>
.cesium-viewer {
  width: 100%;
  height: 100%;
  margin: 0;
  padding: 0;
  overflow: hidden;
  background: #000000;
}

/* Cesium控件样式定制 */
:deep(.cesium-viewer-cesiumWidgetContainer) {
  background: #000000;
}

:deep(.cesium-widget-credits) {
  display: none !important;
}

:deep(.cesium-viewer-toolbar) {
  background: rgba(42, 42, 42, 0.8);
  border-radius: 5px;
  margin: 10px;
}

:deep(.cesium-button) {
  background: rgba(42, 42, 42, 0.8);
  border: 1px solid #404040;
  color: #ffffff;
}

:deep(.cesium-button:hover) {
  background: rgba(255, 215, 0, 0.8);
  color: #1a1a1a;
}

:deep(.cesium-viewer-geocoderContainer) {
  background: rgba(42, 42, 42, 0.8);
  border-radius: 5px;
}

:deep(.cesium-geocoder-input) {
  background: rgba(26, 26, 26, 0.9);
  border: 1px solid #404040;
  color: #ffffff;
}

:deep(.cesium-geocoder-input:focus) {
  border-color: #ffd700;
}
</style>
