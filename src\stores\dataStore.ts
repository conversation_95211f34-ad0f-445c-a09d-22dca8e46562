import { defineStore } from 'pinia'
import { ref, computed } from 'vue'

// 数据类型定义
export interface TerrainData {
  id: string
  name: string
  type: 'dem' | 'contour' | 'point_cloud'
  format: string
  size: number
  uploadTime: Date
  visible: boolean
  opacity: number
  data?: any
}

export interface DrillHoleData {
  id: string
  name: string
  location: {
    longitude: number
    latitude: number
    elevation: number
  }
  depth: number
  diameter: number
  samples: Array<{
    depth: number
    rockType: string
    hardness: number
    description: string
  }>
  uploadTime: Date
  visible: boolean
}

export interface GeologyData {
  id: string
  name: string
  type: 'stratum' | 'fault' | 'ore_body'
  geometry: any
  properties: Record<string, any>
  uploadTime: Date
  visible: boolean
  style: {
    color: string
    opacity: number
    fillColor: string
    fillOpacity: number
  }
}

export interface CADData {
  id: string
  name: string
  format: 'dwg' | 'dxf' | 'dgn'
  layers: Array<{
    name: string
    visible: boolean
    color: string
    entities: any[]
  }>
  uploadTime: Date
  visible: boolean
}

export const useDataStore = defineStore('data', () => {
  // 状态
  const terrainDataList = ref<TerrainData[]>([])
  const drillHoleDataList = ref<DrillHoleData[]>([])
  const geologyDataList = ref<GeologyData[]>([])
  const cadDataList = ref<CADData[]>([])
  
  const isLoading = ref(false)
  const currentDataType = ref<'terrain' | 'drillhole' | 'geology' | 'cad'>('terrain')

  // 计算属性
  const visibleTerrainData = computed(() => 
    terrainDataList.value.filter(item => item.visible)
  )
  
  const visibleDrillHoleData = computed(() => 
    drillHoleDataList.value.filter(item => item.visible)
  )
  
  const visibleGeologyData = computed(() => 
    geologyDataList.value.filter(item => item.visible)
  )
  
  const visibleCADData = computed(() => 
    cadDataList.value.filter(item => item.visible)
  )

  const totalDataCount = computed(() => 
    terrainDataList.value.length + 
    drillHoleDataList.value.length + 
    geologyDataList.value.length + 
    cadDataList.value.length
  )

  // 地形数据操作
  const addTerrainData = (data: Omit<TerrainData, 'id' | 'uploadTime'>) => {
    const newData: TerrainData = {
      ...data,
      id: generateId(),
      uploadTime: new Date()
    }
    terrainDataList.value.push(newData)
    return newData
  }

  const updateTerrainData = (id: string, updates: Partial<TerrainData>) => {
    const index = terrainDataList.value.findIndex(item => item.id === id)
    if (index !== -1) {
      terrainDataList.value[index] = { ...terrainDataList.value[index], ...updates }
    }
  }

  const removeTerrainData = (id: string) => {
    const index = terrainDataList.value.findIndex(item => item.id === id)
    if (index !== -1) {
      terrainDataList.value.splice(index, 1)
    }
  }

  const toggleTerrainDataVisibility = (id: string) => {
    const data = terrainDataList.value.find(item => item.id === id)
    if (data) {
      data.visible = !data.visible
    }
  }

  // 钻孔数据操作
  const addDrillHoleData = (data: Omit<DrillHoleData, 'id' | 'uploadTime'>) => {
    const newData: DrillHoleData = {
      ...data,
      id: generateId(),
      uploadTime: new Date()
    }
    drillHoleDataList.value.push(newData)
    return newData
  }

  const updateDrillHoleData = (id: string, updates: Partial<DrillHoleData>) => {
    const index = drillHoleDataList.value.findIndex(item => item.id === id)
    if (index !== -1) {
      drillHoleDataList.value[index] = { ...drillHoleDataList.value[index], ...updates }
    }
  }

  const removeDrillHoleData = (id: string) => {
    const index = drillHoleDataList.value.findIndex(item => item.id === id)
    if (index !== -1) {
      drillHoleDataList.value.splice(index, 1)
    }
  }

  // 地质数据操作
  const addGeologyData = (data: Omit<GeologyData, 'id' | 'uploadTime'>) => {
    const newData: GeologyData = {
      ...data,
      id: generateId(),
      uploadTime: new Date()
    }
    geologyDataList.value.push(newData)
    return newData
  }

  const updateGeologyData = (id: string, updates: Partial<GeologyData>) => {
    const index = geologyDataList.value.findIndex(item => item.id === id)
    if (index !== -1) {
      geologyDataList.value[index] = { ...geologyDataList.value[index], ...updates }
    }
  }

  const removeGeologyData = (id: string) => {
    const index = geologyDataList.value.findIndex(item => item.id === id)
    if (index !== -1) {
      geologyDataList.value.splice(index, 1)
    }
  }

  // CAD数据操作
  const addCADData = (data: Omit<CADData, 'id' | 'uploadTime'>) => {
    const newData: CADData = {
      ...data,
      id: generateId(),
      uploadTime: new Date()
    }
    cadDataList.value.push(newData)
    return newData
  }

  const updateCADData = (id: string, updates: Partial<CADData>) => {
    const index = cadDataList.value.findIndex(item => item.id === id)
    if (index !== -1) {
      cadDataList.value[index] = { ...cadDataList.value[index], ...updates }
    }
  }

  const removeCADData = (id: string) => {
    const index = cadDataList.value.findIndex(item => item.id === id)
    if (index !== -1) {
      cadDataList.value.splice(index, 1)
    }
  }

  // 通用操作
  const clearAllData = () => {
    terrainDataList.value = []
    drillHoleDataList.value = []
    geologyDataList.value = []
    cadDataList.value = []
  }

  const exportData = (type: 'terrain' | 'drillhole' | 'geology' | 'cad' | 'all') => {
    let dataToExport: any = {}
    
    switch (type) {
      case 'terrain':
        dataToExport = { terrain: terrainDataList.value }
        break
      case 'drillhole':
        dataToExport = { drillhole: drillHoleDataList.value }
        break
      case 'geology':
        dataToExport = { geology: geologyDataList.value }
        break
      case 'cad':
        dataToExport = { cad: cadDataList.value }
        break
      case 'all':
        dataToExport = {
          terrain: terrainDataList.value,
          drillhole: drillHoleDataList.value,
          geology: geologyDataList.value,
          cad: cadDataList.value
        }
        break
    }

    const blob = new Blob([JSON.stringify(dataToExport, null, 2)], {
      type: 'application/json'
    })
    
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = `${type}_data_${new Date().toISOString().split('T')[0]}.json`
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
    URL.revokeObjectURL(url)
  }

  // 工具函数
  const generateId = () => {
    return Date.now().toString(36) + Math.random().toString(36).substr(2)
  }

  return {
    // 状态
    terrainDataList,
    drillHoleDataList,
    geologyDataList,
    cadDataList,
    isLoading,
    currentDataType,
    
    // 计算属性
    visibleTerrainData,
    visibleDrillHoleData,
    visibleGeologyData,
    visibleCADData,
    totalDataCount,
    
    // 地形数据操作
    addTerrainData,
    updateTerrainData,
    removeTerrainData,
    toggleTerrainDataVisibility,
    
    // 钻孔数据操作
    addDrillHoleData,
    updateDrillHoleData,
    removeDrillHoleData,
    
    // 地质数据操作
    addGeologyData,
    updateGeologyData,
    removeGeologyData,
    
    // CAD数据操作
    addCADData,
    updateCADData,
    removeCADData,
    
    // 通用操作
    clearAllData,
    exportData
  }
})
