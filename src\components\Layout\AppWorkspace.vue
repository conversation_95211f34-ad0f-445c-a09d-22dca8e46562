<template>
  <div class="app-workspace">
    <!-- 主工作区 -->
    <div class="workspace-main">
      <div class="workspace-content">
        <!-- 这里将放置Cesium地图组件 -->
        <div id="cesium-container" class="cesium-container">
          <!-- Cesium地图将在这里渲染 -->
        </div>
        
        <!-- 底部图表区域 -->
        <div class="chart-section" v-if="showChart">
          <div class="chart-header">
            <h4>道路剖面图</h4>
            <el-button type="text" @click="toggleChart" class="close-btn">
              <el-icon><Close /></el-icon>
            </el-button>
          </div>
          <div class="chart-content">
            <!-- 这里将放置道路剖面图 -->
            <div class="profile-chart">
              <canvas id="profile-canvas" width="800" height="200"></canvas>
            </div>
          </div>
        </div>
      </div>
    </div>
    
    <!-- 右侧悬浮工具栏 -->
    <div class="floating-toolbar">
      <div class="toolbar-group">
        <div class="group-title">视图控制</div>
        <el-tooltip content="2D视图" placement="left">
          <el-button 
            type="primary" 
            circle 
            size="large"
            :class="{ active: viewMode === '2d' }"
            @click="setViewMode('2d')"
          >
            <el-icon><Grid /></el-icon>
          </el-button>
        </el-tooltip>
        <el-tooltip content="3D视图" placement="left">
          <el-button 
            type="primary" 
            circle 
            size="large"
            :class="{ active: viewMode === '3d' }"
            @click="setViewMode('3d')"
          >
            <el-icon><Box /></el-icon>
          </el-button>
        </el-tooltip>
        <el-tooltip content="重置视图" placement="left">
          <el-button type="warning" circle size="large" @click="resetView">
            <el-icon><Refresh /></el-icon>
          </el-button>
        </el-tooltip>
      </div>
      
      <div class="toolbar-group">
        <div class="group-title">绘制工具</div>
        <el-tooltip content="绘制道路" placement="left">
          <el-button 
            type="success" 
            circle 
            size="large"
            :class="{ active: drawMode === 'road' }"
            @click="setDrawMode('road')"
          >
            <el-icon><Connection /></el-icon>
          </el-button>
        </el-tooltip>
        <el-tooltip content="测量距离" placement="left">
          <el-button 
            type="info" 
            circle 
            size="large"
            :class="{ active: drawMode === 'measure' }"
            @click="setDrawMode('measure')"
          >
            <el-icon><Position /></el-icon>
          </el-button>
        </el-tooltip>
        <el-tooltip content="标注点" placement="left">
          <el-button 
            type="warning" 
            circle 
            size="large"
            :class="{ active: drawMode === 'point' }"
            @click="setDrawMode('point')"
          >
            <el-icon><Location /></el-icon>
          </el-button>
        </el-tooltip>
      </div>
      
      <div class="toolbar-group">
        <div class="group-title">分析工具</div>
        <el-tooltip content="剖面分析" placement="left">
          <el-button type="primary" circle size="large" @click="showProfileAnalysis">
            <el-icon><TrendCharts /></el-icon>
          </el-button>
        </el-tooltip>
        <el-tooltip content="坡度分析" placement="left">
          <el-button type="danger" circle size="large" @click="showSlopeAnalysis">
            <el-icon><Warning /></el-icon>
          </el-button>
        </el-tooltip>
        <el-tooltip content="可视性分析" placement="left">
          <el-button type="success" circle size="large" @click="showVisibilityAnalysis">
            <el-icon><View /></el-icon>
          </el-button>
        </el-tooltip>
      </div>
      
      <div class="toolbar-group">
        <div class="group-title">图层控制</div>
        <el-tooltip content="显示/隐藏图层" placement="left">
          <el-button type="info" circle size="large" @click="toggleLayers">
            <el-icon><Grid /></el-icon>
          </el-button>
        </el-tooltip>
        <el-tooltip content="图例说明" placement="left">
          <el-button type="warning" circle size="large" @click="showLegend">
            <el-icon><InfoFilled /></el-icon>
          </el-button>
        </el-tooltip>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import {
  Close, Grid, Box, Refresh, Connection, Position,
  Location, TrendCharts, Warning, View, InfoFilled
} from '@element-plus/icons-vue'

const showChart = ref(false)
const viewMode = ref('3d')
const drawMode = ref('')
const cesiumViewerRef = ref()

// Cesium配置
const terrainProvider = ref('ellipsoid')
const imageryProvider = ref('osm')
const homeView = ref({
  longitude: 116.4074, // 北京经度
  latitude: 39.9042,   // 北京纬度
  height: 15000        // 高度
})

// Cesium事件处理
const onViewerReady = (viewer: any) => {
  console.log('Cesium viewer ready:', viewer)
}

const onViewChanged = (view: any) => {
  console.log('View changed:', view)
}

const onEntitySelected = (entity: any) => {
  console.log('Entity selected:', entity)
}

const setViewMode = (mode: string) => {
  viewMode.value = mode
  console.log('View mode changed to:', mode)

  // 切换Cesium视图模式
  if (cesiumViewerRef.value) {
    if (mode === '2d') {
      cesiumViewerRef.value.setView2D()
    } else if (mode === '3d') {
      cesiumViewerRef.value.setView3D()
    }
  }
}

const setDrawMode = (mode: string) => {
  if (drawMode.value === mode) {
    drawMode.value = ''
  } else {
    drawMode.value = mode
  }
  console.log('Draw mode changed to:', drawMode.value)
  // 这里将实现绘制模式切换逻辑
}

const resetView = () => {
  console.log('Reset view')
  // 这里将实现视图重置逻辑
}

const showProfileAnalysis = () => {
  showChart.value = true
  console.log('Show profile analysis')
  // 这里将实现剖面分析逻辑
}

const showSlopeAnalysis = () => {
  console.log('Show slope analysis')
  // 这里将实现坡度分析逻辑
}

const showVisibilityAnalysis = () => {
  console.log('Show visibility analysis')
  // 这里将实现可视性分析逻辑
}

const toggleLayers = () => {
  console.log('Toggle layers')
  // 这里将实现图层控制逻辑
}

const showLegend = () => {
  console.log('Show legend')
  // 这里将实现图例显示逻辑
}

const toggleChart = () => {
  showChart.value = !showChart.value
}

onMounted(() => {
  // 初始化Cesium地图
  console.log('Workspace mounted, ready to initialize Cesium')
})
</script>

<style scoped>
.app-workspace {
  flex: 1;
  display: flex;
  position: relative;
  background: #1a1a1a;
}

.workspace-main {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.workspace-content {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.cesium-container {
  flex: 1;
  width: 100%;
  background: #000000;
  position: relative;
}

.chart-section {
  height: 250px;
  background: #2a2a2a;
  border-top: 1px solid #404040;
  display: flex;
  flex-direction: column;
}

.chart-header {
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 15px;
  background: #333333;
  border-bottom: 1px solid #404040;
}

.chart-header h4 {
  color: #ffd700;
  margin: 0;
  font-size: 14px;
}

.close-btn {
  color: #cccccc;
  padding: 5px;
}

.chart-content {
  flex: 1;
  padding: 15px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.profile-chart {
  width: 100%;
  height: 100%;
  background: #1a1a1a;
  border: 1px solid #404040;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.floating-toolbar {
  position: absolute;
  right: 20px;
  top: 20px;
  z-index: 1000;
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.toolbar-group {
  background: rgba(42, 42, 42, 0.95);
  border: 1px solid #404040;
  border-radius: 8px;
  padding: 15px;
  display: flex;
  flex-direction: column;
  gap: 10px;
  backdrop-filter: blur(10px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

.group-title {
  color: #ffd700;
  font-size: 12px;
  font-weight: bold;
  text-align: center;
  margin-bottom: 5px;
  border-bottom: 1px solid #404040;
  padding-bottom: 5px;
}

.toolbar-group .el-button {
  margin: 0;
  width: 45px;
  height: 45px;
  background: #404040;
  border-color: #404040;
  color: #cccccc;
  transition: all 0.3s ease;
}

.toolbar-group .el-button:hover {
  background: #ffd700;
  border-color: #ffd700;
  color: #1a1a1a;
  transform: scale(1.05);
}

.toolbar-group .el-button.active {
  background: #ffd700;
  border-color: #ffd700;
  color: #1a1a1a;
}

.toolbar-group .el-button.el-button--warning {
  background: #e6a23c;
  border-color: #e6a23c;
}

.toolbar-group .el-button.el-button--warning:hover {
  background: #f7ba2a;
  border-color: #f7ba2a;
}

.toolbar-group .el-button.el-button--success {
  background: #67c23a;
  border-color: #67c23a;
}

.toolbar-group .el-button.el-button--success:hover {
  background: #85ce61;
  border-color: #85ce61;
}

.toolbar-group .el-button.el-button--danger {
  background: #f56c6c;
  border-color: #f56c6c;
}

.toolbar-group .el-button.el-button--danger:hover {
  background: #f78989;
  border-color: #f78989;
}

.toolbar-group .el-button.el-button--info {
  background: #909399;
  border-color: #909399;
}

.toolbar-group .el-button.el-button--info:hover {
  background: #a6a9ad;
  border-color: #a6a9ad;
}
</style>
