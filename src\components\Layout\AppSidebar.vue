<template>
  <div class="app-sidebar" :class="{ 'collapsed': collapsed }">
    <div class="sidebar-header">
      <h3 v-if="!collapsed">数据操作区</h3>
      <el-button 
        type="text" 
        @click="toggleCollapse"
        class="collapse-btn"
      >
        <el-icon>
          <ArrowLeft v-if="!collapsed" />
          <ArrowRight v-if="collapsed" />
        </el-icon>
      </el-button>
    </div>
    
    <div class="sidebar-content">
      <!-- 数据输入区 -->
      <div class="data-section" v-if="!collapsed">
        <div class="section-title">
          <el-icon><Upload /></el-icon>
          数据输入
        </div>
        <div class="section-content">
          <el-button type="primary" size="small" class="action-btn">
            <el-icon><FolderOpened /></el-icon>
            导入地形
          </el-button>
          <el-button type="primary" size="small" class="action-btn">
            <el-icon><DocumentAdd /></el-icon>
            导入CAD
          </el-button>
          <el-button type="primary" size="small" class="action-btn">
            <el-icon><Position /></el-icon>
            钻孔数据
          </el-button>
        </div>
      </div>
      
      <!-- 图层管理 -->
      <div class="data-section" v-if="!collapsed">
        <div class="section-title">
          <el-icon><Grid /></el-icon>
          图层管理
        </div>
        <div class="section-content">
          <el-tree
            :data="layerData"
            show-checkbox
            node-key="id"
            :default-expanded-keys="[1]"
            :default-checked-keys="[1, 2]"
            class="layer-tree"
          >
            <template #default="{ node, data }">
              <span class="layer-node">
                <el-icon>
                  <component :is="data.icon" />
                </el-icon>
                <span>{{ node.label }}</span>
              </span>
            </template>
          </el-tree>
        </div>
      </div>
      
      <!-- 属性面板 -->
      <div class="data-section" v-if="!collapsed">
        <div class="section-title">
          <el-icon><Setting /></el-icon>
          属性设置
        </div>
        <div class="section-content">
          <el-form size="small" label-width="80px">
            <el-form-item label="道路宽度">
              <el-input-number v-model="roadWidth" :min="3" :max="20" size="small" />
            </el-form-item>
            <el-form-item label="最大坡度">
              <el-input-number v-model="maxSlope" :min="1" :max="15" size="small" />
            </el-form-item>
            <el-form-item label="转弯半径">
              <el-input-number v-model="turnRadius" :min="10" :max="100" size="small" />
            </el-form-item>
          </el-form>
        </div>
      </div>
    </div>
    
    <!-- 数据输出显示区 -->
    <div class="output-section" v-if="!collapsed">
      <div class="section-title">
        <el-icon><DataLine /></el-icon>
        数据输出
      </div>
      <div class="output-content">
        <div class="output-item">
          <span class="label">总长度:</span>
          <span class="value">1,250.5m</span>
        </div>
        <div class="output-item">
          <span class="label">平均坡度:</span>
          <span class="value">8.5%</span>
        </div>
        <div class="output-item">
          <span class="label">转弯数量:</span>
          <span class="value">12个</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import {
  ArrowLeft, ArrowRight, Upload, FolderOpened,
  DocumentAdd, Position, Grid, Setting, DataLine
} from '@element-plus/icons-vue'

const collapsed = ref(false)
const roadWidth = ref(6)
const maxSlope = ref(10)
const turnRadius = ref(30)

const layerData = ref([
  {
    id: 1,
    label: '地形数据',
    icon: 'MapLocation',
    children: [
      { id: 2, label: 'DEM高程', icon: 'TrendCharts' },
      { id: 3, label: '等高线', icon: 'Connection' }
    ]
  },
  {
    id: 4,
    label: '道路设计',
    icon: 'Connection',
    children: [
      { id: 5, label: '主干道', icon: 'Connection' },
      { id: 6, label: '支路', icon: 'Connection' }
    ]
  },
  {
    id: 7,
    label: '安全区域',
    icon: 'Warning',
    children: [
      { id: 8, label: '危险区域', icon: 'Warning' },
      { id: 9, label: '安全缓冲', icon: 'CircleCheck' }
    ]
  }
])

const toggleCollapse = () => {
  collapsed.value = !collapsed.value
}
</script>

<style scoped>
.app-sidebar {
  width: 320px;
  background: linear-gradient(180deg, #2a2a2a 0%, #1e1e1e 100%);
  border-right: 1px solid #404040;
  display: flex;
  flex-direction: column;
  transition: width 0.3s ease;
  overflow: hidden;
}

.app-sidebar.collapsed {
  width: 60px;
}

.sidebar-header {
  height: 50px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 15px;
  border-bottom: 1px solid #404040;
  background: #333333;
}

.sidebar-header h3 {
  color: #ffd700;
  margin: 0;
  font-size: 16px;
}

.collapse-btn {
  color: #cccccc;
  padding: 5px;
}

.sidebar-content {
  flex: 1;
  padding: 15px;
  overflow-y: auto;
}

.data-section {
  margin-bottom: 20px;
}

.section-title {
  display: flex;
  align-items: center;
  color: #ffd700;
  font-weight: bold;
  margin-bottom: 10px;
  font-size: 14px;
}

.section-title .el-icon {
  margin-right: 8px;
}

.section-content {
  padding-left: 20px;
}

.action-btn {
  display: block;
  width: 100%;
  margin-bottom: 8px;
  background: #404040;
  border-color: #404040;
  color: #ffffff;
}

.action-btn:hover {
  background: #ffd700;
  border-color: #ffd700;
  color: #1a1a1a;
}

.layer-tree {
  background: transparent;
  color: #cccccc;
}

.layer-node {
  display: flex;
  align-items: center;
}

.layer-node .el-icon {
  margin-right: 5px;
  color: #ffd700;
}

.output-section {
  border-top: 1px solid #404040;
  padding: 15px;
  background: #1a1a1a;
}

.output-content {
  padding-left: 20px;
}

.output-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
  color: #cccccc;
  font-size: 12px;
}

.output-item .label {
  color: #999999;
}

.output-item .value {
  color: #ffd700;
  font-weight: bold;
}

/* 滚动条样式 */
.sidebar-content::-webkit-scrollbar {
  width: 6px;
}

.sidebar-content::-webkit-scrollbar-track {
  background: #1a1a1a;
}

.sidebar-content::-webkit-scrollbar-thumb {
  background: #404040;
  border-radius: 3px;
}

.sidebar-content::-webkit-scrollbar-thumb:hover {
  background: #555555;
}
</style>
