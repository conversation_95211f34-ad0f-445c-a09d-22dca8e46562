<template>
  <div class="app-layout">
    <!-- 顶部标题栏 -->
    <AppHeader />
    
    <!-- 主体内容区 -->
    <div class="app-body">
      <!-- 左侧边栏 -->
      <AppSidebar />

      <!-- 中间工作区 -->
      <AppWorkspace />
    </div>
  </div>
</template>

<script setup lang="ts">
import AppHeader from './AppHeader.vue'
import AppSidebar from './AppSidebar.vue'
import AppWorkspace from './AppWorkspace.vue'

// 定义组件名称
defineOptions({
  name: 'AppLayout'
})
</script>

<style scoped>
.app-layout {
  height: 100vh;
  width: 100vw;
  display: flex;
  flex-direction: column;
  background: #1a1a1a;
  overflow: hidden;
  position: fixed;
  top: 0;
  left: 0;
}

.app-body {
  flex: 1;
  display: flex;
  overflow: hidden;
}

.app-main {
  flex: 1;
  overflow: hidden;
  background: #1a1a1a;
}
</style>
