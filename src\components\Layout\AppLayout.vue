<template>
  <div class="app-layout">
    <!-- 顶部标题栏 -->
    <AppHeader />
    
    <!-- 主体内容区 -->
    <div class="app-body">
      <!-- 左侧边栏 -->
      <AppSidebar />

      <!-- 中间工作区 - 路由视图 -->
      <div class="app-main">
        <router-view />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import AppHeader from './AppHeader.vue'
import AppSidebar from './AppSidebar.vue'

// 定义组件名称
defineOptions({
  name: 'AppLayout'
})
</script>

<style scoped>
.app-layout {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: #1a1a1a;
  overflow: hidden;
}

.app-body {
  flex: 1;
  display: flex;
  overflow: hidden;
}

.app-main {
  flex: 1;
  overflow: hidden;
  background: #1a1a1a;
}
</style>
