<template>
  <div class="cad-viewer">
    <div class="cad-toolbar">
      <div class="toolbar-left">
        <el-button-group>
          <el-button 
            type="primary" 
            size="small"
            :class="{ active: viewMode === 'fit' }"
            @click="fitToView"
          >
            <el-icon><FullScreen /></el-icon>
            适应视图
          </el-button>
          <el-button 
            type="primary" 
            size="small"
            :class="{ active: viewMode === 'zoom' }"
            @click="setZoomMode"
          >
            <el-icon><ZoomIn /></el-icon>
            缩放
          </el-button>
          <el-button 
            type="primary" 
            size="small"
            :class="{ active: viewMode === 'pan' }"
            @click="setPanMode"
          >
            <el-icon><Rank /></el-icon>
            平移
          </el-button>
        </el-button-group>
        
        <el-divider direction="vertical" />
        
        <el-button-group>
          <el-button type="success" size="small" @click="showLayerManager">
            <el-icon><Grid /></el-icon>
            图层
          </el-button>
          <el-button type="info" size="small" @click="showProperties">
            <el-icon><InfoFilled /></el-icon>
            属性
          </el-button>
          <el-button type="warning" size="small" @click="showMeasure">
            <el-icon><Ruler /></el-icon>
            测量
          </el-button>
        </el-button-group>
      </div>
      
      <div class="toolbar-right">
        <el-input-number 
          v-model="zoomLevel" 
          :min="10" 
          :max="1000" 
          :step="10"
          size="small"
          @change="onZoomChange"
        />
        <span class="zoom-label">%</span>
        
        <el-divider direction="vertical" />
        
        <el-button type="primary" size="small" @click="exportCAD">
          <el-icon><Download /></el-icon>
          导出
        </el-button>
      </div>
    </div>

    <div class="cad-content">
      <div class="cad-canvas-container" ref="canvasContainer">
        <canvas 
          ref="cadCanvas" 
          class="cad-canvas"
          @mousedown="onMouseDown"
          @mousemove="onMouseMove"
          @mouseup="onMouseUp"
          @wheel="onWheel"
        ></canvas>
        
        <!-- 坐标显示 -->
        <div class="coordinate-display">
          <span>X: {{ currentCoords.x.toFixed(2) }}</span>
          <span>Y: {{ currentCoords.y.toFixed(2) }}</span>
        </div>
      </div>
      
      <!-- 图层管理面板 -->
      <div v-if="showLayerPanel" class="layer-panel">
        <div class="panel-header">
          <h4>图层管理</h4>
          <el-button type="text" @click="showLayerPanel = false">
            <el-icon><Close /></el-icon>
          </el-button>
        </div>
        <div class="panel-content">
          <el-tree
            :data="layerTreeData"
            show-checkbox
            node-key="id"
            :default-checked-keys="visibleLayers"
            @check="onLayerCheck"
          >
            <template #default="{ node, data }">
              <span class="layer-node">
                <div 
                  class="layer-color" 
                  :style="{ backgroundColor: data.color }"
                ></div>
                <span>{{ node.label }}</span>
                <span class="entity-count">({{ data.entityCount }})</span>
              </span>
            </template>
          </el-tree>
        </div>
      </div>
      
      <!-- 属性面板 -->
      <div v-if="showPropertiesPanel" class="properties-panel">
        <div class="panel-header">
          <h4>实体属性</h4>
          <el-button type="text" @click="showPropertiesPanel = false">
            <el-icon><Close /></el-icon>
          </el-button>
        </div>
        <div class="panel-content">
          <el-form v-if="selectedEntity" label-width="80px" size="small">
            <el-form-item label="类型">
              <el-input v-model="selectedEntity.type" readonly />
            </el-form-item>
            <el-form-item label="图层">
              <el-input v-model="selectedEntity.layer" readonly />
            </el-form-item>
            <el-form-item label="颜色">
              <el-color-picker v-model="selectedEntity.color" @change="updateEntityColor" />
            </el-form-item>
            <el-form-item label="线型">
              <el-select v-model="selectedEntity.lineType" @change="updateEntityLineType">
                <el-option label="连续线" value="CONTINUOUS" />
                <el-option label="虚线" value="DASHED" />
                <el-option label="中心线" value="CENTER" />
                <el-option label="点划线" value="DASHDOT" />
              </el-select>
            </el-form-item>
            <el-form-item label="线宽">
              <el-input-number 
                v-model="selectedEntity.lineWeight" 
                :min="1" 
                :max="10"
                @change="updateEntityLineWeight"
              />
            </el-form-item>
          </el-form>
          <div v-else class="no-selection">
            <p>请选择一个实体查看属性</p>
          </div>
        </div>
      </div>
    </div>

    <!-- 测量工具 -->
    <div v-if="showMeasurePanel" class="measure-panel">
      <div class="panel-header">
        <h4>测量工具</h4>
        <el-button type="text" @click="showMeasurePanel = false">
          <el-icon><Close /></el-icon>
        </el-button>
      </div>
      <div class="panel-content">
        <el-button-group>
          <el-button 
            type="primary" 
            size="small"
            :class="{ active: measureMode === 'distance' }"
            @click="setMeasureMode('distance')"
          >
            距离
          </el-button>
          <el-button 
            type="primary" 
            size="small"
            :class="{ active: measureMode === 'area' }"
            @click="setMeasureMode('area')"
          >
            面积
          </el-button>
          <el-button 
            type="primary" 
            size="small"
            :class="{ active: measureMode === 'angle' }"
            @click="setMeasureMode('angle')"
          >
            角度
          </el-button>
        </el-button-group>
        
        <div class="measure-results">
          <div v-if="measureResult.distance">
            <label>距离:</label>
            <span>{{ measureResult.distance.toFixed(2) }} mm</span>
          </div>
          <div v-if="measureResult.area">
            <label>面积:</label>
            <span>{{ measureResult.area.toFixed(2) }} mm²</span>
          </div>
          <div v-if="measureResult.angle">
            <label>角度:</label>
            <span>{{ measureResult.angle.toFixed(2) }}°</span>
          </div>
        </div>
        
        <el-button type="danger" size="small" @click="clearMeasurements">
          清除测量
        </el-button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, onUnmounted, nextTick } from 'vue'
import { ElMessage } from 'element-plus'
import { 
  FullScreen, ZoomIn, Rank, Grid, InfoFilled, Ruler,
  Download, Close 
} from '@element-plus/icons-vue'
import type { CADDocument, CADEntity } from '@/utils/cadParser'

// 定义组件名称
defineOptions({
  name: 'CADViewer'
})

// Props
interface Props {
  cadDocument?: CADDocument
  width?: number
  height?: number
}

const props = withDefaults(defineProps<Props>(), {
  width: 800,
  height: 600
})

// Emits
const emit = defineEmits<{
  entitySelected: [entity: CADEntity]
  layerToggled: [layerName: string, visible: boolean]
  entityModified: [entity: CADEntity]
}>()

// 响应式数据
const canvasContainer = ref<HTMLElement>()
const cadCanvas = ref<HTMLCanvasElement>()
const viewMode = ref('pan')
const zoomLevel = ref(100)
const currentCoords = reactive({ x: 0, y: 0 })

// 面板显示状态
const showLayerPanel = ref(false)
const showPropertiesPanel = ref(false)
const showMeasurePanel = ref(false)

// 图层数据
const layerTreeData = ref<any[]>([])
const visibleLayers = ref<string[]>([])

// 选中的实体
const selectedEntity = ref<CADEntity | null>(null)

// 测量相关
const measureMode = ref('')
const measureResult = reactive({
  distance: 0,
  area: 0,
  angle: 0
})

// Canvas相关
let ctx: CanvasRenderingContext2D | null = null
let isDrawing = false
let lastMousePos = { x: 0, y: 0 }
let viewTransform = {
  scale: 1,
  translateX: 0,
  translateY: 0
}

// 生命周期
onMounted(() => {
  initCanvas()
  if (props.cadDocument) {
    loadCADDocument(props.cadDocument)
  }
})

onUnmounted(() => {
  // 清理资源
})

// 方法
const initCanvas = async () => {
  await nextTick()
  
  if (!cadCanvas.value || !canvasContainer.value) return
  
  const canvas = cadCanvas.value
  const container = canvasContainer.value
  
  // 设置canvas尺寸
  canvas.width = container.clientWidth
  canvas.height = container.clientHeight
  
  ctx = canvas.getContext('2d')
  if (!ctx) return
  
  // 设置默认样式
  ctx.lineCap = 'round'
  ctx.lineJoin = 'round'
  
  // 绘制网格
  drawGrid()
}

const loadCADDocument = (document: CADDocument) => {
  // 构建图层树数据
  layerTreeData.value = document.layers.map(layer => ({
    id: layer.name,
    label: layer.name,
    color: layer.color,
    entityCount: layer.entities.length,
    visible: layer.visible
  }))
  
  // 设置可见图层
  visibleLayers.value = document.layers
    .filter(layer => layer.visible)
    .map(layer => layer.name)
  
  // 绘制CAD内容
  drawCADDocument(document)
}

const drawCADDocument = (document: CADDocument) => {
  if (!ctx) return
  
  // 清空画布
  ctx.clearRect(0, 0, cadCanvas.value!.width, cadCanvas.value!.height)
  
  // 绘制网格
  drawGrid()
  
  // 应用变换
  ctx.save()
  ctx.scale(viewTransform.scale, viewTransform.scale)
  ctx.translate(viewTransform.translateX, viewTransform.translateY)
  
  // 绘制各图层
  document.layers.forEach(layer => {
    if (!layer.visible || !visibleLayers.value.includes(layer.name)) return
    
    layer.entities.forEach(entity => {
      drawEntity(entity)
    })
  })
  
  ctx.restore()
}

const drawGrid = () => {
  if (!ctx) return
  
  const canvas = cadCanvas.value!
  const gridSize = 20
  
  ctx.save()
  ctx.strokeStyle = '#333333'
  ctx.lineWidth = 0.5
  
  // 绘制垂直线
  for (let x = 0; x <= canvas.width; x += gridSize) {
    ctx.beginPath()
    ctx.moveTo(x, 0)
    ctx.lineTo(x, canvas.height)
    ctx.stroke()
  }
  
  // 绘制水平线
  for (let y = 0; y <= canvas.height; y += gridSize) {
    ctx.beginPath()
    ctx.moveTo(0, y)
    ctx.lineTo(canvas.width, y)
    ctx.stroke()
  }
  
  ctx.restore()
}

const drawEntity = (entity: CADEntity) => {
  if (!ctx) return
  
  ctx.save()
  ctx.strokeStyle = entity.color
  ctx.lineWidth = entity.lineWeight
  
  // 设置线型
  if (entity.lineType === 'DASHED') {
    ctx.setLineDash([5, 5])
  } else if (entity.lineType === 'CENTER') {
    ctx.setLineDash([10, 5, 2, 5])
  } else if (entity.lineType === 'DASHDOT') {
    ctx.setLineDash([10, 5, 2, 5, 2, 5])
  }
  
  switch (entity.type) {
    case 'LINE':
      drawLine(entity.geometry)
      break
    case 'CIRCLE':
      drawCircle(entity.geometry)
      break
    case 'POLYLINE':
      drawPolyline(entity.geometry)
      break
    case 'POINT':
      drawPoint(entity.geometry)
      break
  }
  
  ctx.restore()
}

const drawLine = (geometry: any) => {
  if (!ctx) return
  
  ctx.beginPath()
  ctx.moveTo(geometry.startPoint.x, geometry.startPoint.y)
  ctx.lineTo(geometry.endPoint.x, geometry.endPoint.y)
  ctx.stroke()
}

const drawCircle = (geometry: any) => {
  if (!ctx) return
  
  ctx.beginPath()
  ctx.arc(geometry.center.x, geometry.center.y, geometry.radius, 0, 2 * Math.PI)
  ctx.stroke()
}

const drawPolyline = (geometry: any) => {
  if (!ctx || !geometry.points || geometry.points.length < 2) return
  
  ctx.beginPath()
  ctx.moveTo(geometry.points[0].x, geometry.points[0].y)
  
  for (let i = 1; i < geometry.points.length; i++) {
    ctx.lineTo(geometry.points[i].x, geometry.points[i].y)
  }
  
  ctx.stroke()
}

const drawPoint = (geometry: any) => {
  if (!ctx) return
  
  ctx.beginPath()
  ctx.arc(geometry.x, geometry.y, 2, 0, 2 * Math.PI)
  ctx.fill()
}

// 事件处理
const onMouseDown = (event: MouseEvent) => {
  isDrawing = true
  lastMousePos = { x: event.offsetX, y: event.offsetY }
}

const onMouseMove = (event: MouseEvent) => {
  const rect = cadCanvas.value!.getBoundingClientRect()
  currentCoords.x = event.offsetX
  currentCoords.y = event.offsetY
  
  if (isDrawing && viewMode.value === 'pan') {
    const deltaX = event.offsetX - lastMousePos.x
    const deltaY = event.offsetY - lastMousePos.y
    
    viewTransform.translateX += deltaX / viewTransform.scale
    viewTransform.translateY += deltaY / viewTransform.scale
    
    if (props.cadDocument) {
      drawCADDocument(props.cadDocument)
    }
  }
  
  lastMousePos = { x: event.offsetX, y: event.offsetY }
}

const onMouseUp = () => {
  isDrawing = false
}

const onWheel = (event: WheelEvent) => {
  event.preventDefault()
  
  const scaleFactor = event.deltaY > 0 ? 0.9 : 1.1
  const newScale = viewTransform.scale * scaleFactor
  
  if (newScale >= 0.1 && newScale <= 10) {
    viewTransform.scale = newScale
    zoomLevel.value = Math.round(newScale * 100)
    
    if (props.cadDocument) {
      drawCADDocument(props.cadDocument)
    }
  }
}

// 工具栏操作
const fitToView = () => {
  viewMode.value = 'fit'
  viewTransform.scale = 1
  viewTransform.translateX = 0
  viewTransform.translateY = 0
  zoomLevel.value = 100
  
  if (props.cadDocument) {
    drawCADDocument(props.cadDocument)
  }
}

const setZoomMode = () => {
  viewMode.value = 'zoom'
}

const setPanMode = () => {
  viewMode.value = 'pan'
}

const onZoomChange = (value: number) => {
  viewTransform.scale = value / 100
  if (props.cadDocument) {
    drawCADDocument(props.cadDocument)
  }
}

const showLayerManager = () => {
  showLayerPanel.value = !showLayerPanel.value
}

const showProperties = () => {
  showPropertiesPanel.value = !showPropertiesPanel.value
}

const showMeasure = () => {
  showMeasurePanel.value = !showMeasurePanel.value
}

const exportCAD = () => {
  ElMessage.info('导出功能开发中...')
}

// 图层操作
const onLayerCheck = (data: any, checked: any) => {
  const checkedKeys = checked.checkedKeys
  visibleLayers.value = checkedKeys
  
  if (props.cadDocument) {
    drawCADDocument(props.cadDocument)
  }
  
  emit('layerToggled', data.id, checkedKeys.includes(data.id))
}

// 实体属性更新
const updateEntityColor = (color: string) => {
  if (selectedEntity.value) {
    selectedEntity.value.color = color
    emit('entityModified', selectedEntity.value)
    
    if (props.cadDocument) {
      drawCADDocument(props.cadDocument)
    }
  }
}

const updateEntityLineType = (lineType: string) => {
  if (selectedEntity.value) {
    selectedEntity.value.lineType = lineType
    emit('entityModified', selectedEntity.value)
    
    if (props.cadDocument) {
      drawCADDocument(props.cadDocument)
    }
  }
}

const updateEntityLineWeight = (lineWeight: number) => {
  if (selectedEntity.value) {
    selectedEntity.value.lineWeight = lineWeight
    emit('entityModified', selectedEntity.value)
    
    if (props.cadDocument) {
      drawCADDocument(props.cadDocument)
    }
  }
}

// 测量工具
const setMeasureMode = (mode: string) => {
  measureMode.value = mode
  ElMessage.info(`${mode}测量模式已激活`)
}

const clearMeasurements = () => {
  measureResult.distance = 0
  measureResult.area = 0
  measureResult.angle = 0
  ElMessage.success('测量结果已清除')
}

// 暴露方法
defineExpose({
  loadCADDocument,
  fitToView,
  exportCAD
})
</script>

<style scoped>
.cad-viewer {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  background: #1a1a1a;
}

.cad-toolbar {
  height: 50px;
  background: #2a2a2a;
  border-bottom: 1px solid #404040;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 15px;
}

.toolbar-left,
.toolbar-right {
  display: flex;
  align-items: center;
  gap: 10px;
}

.zoom-label {
  color: #cccccc;
  margin-left: 5px;
}

.cad-content {
  flex: 1;
  position: relative;
  overflow: hidden;
}

.cad-canvas-container {
  width: 100%;
  height: 100%;
  position: relative;
}

.cad-canvas {
  width: 100%;
  height: 100%;
  background: #000000;
  cursor: crosshair;
}

.coordinate-display {
  position: absolute;
  bottom: 10px;
  left: 10px;
  background: rgba(42, 42, 42, 0.9);
  color: #ffd700;
  padding: 5px 10px;
  border-radius: 4px;
  font-family: monospace;
  font-size: 12px;
  display: flex;
  gap: 15px;
}

.layer-panel,
.properties-panel,
.measure-panel {
  position: absolute;
  top: 10px;
  right: 10px;
  width: 300px;
  background: rgba(42, 42, 42, 0.95);
  border: 1px solid #404040;
  border-radius: 6px;
  backdrop-filter: blur(10px);
  z-index: 100;
}

.measure-panel {
  width: 250px;
  top: 70px;
}

.panel-header {
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 15px;
  background: #333333;
  border-bottom: 1px solid #404040;
  border-radius: 6px 6px 0 0;
}

.panel-header h4 {
  color: #ffd700;
  margin: 0;
  font-size: 14px;
}

.panel-content {
  padding: 15px;
  max-height: 400px;
  overflow-y: auto;
}

.layer-node {
  display: flex;
  align-items: center;
  gap: 8px;
  width: 100%;
}

.layer-color {
  width: 12px;
  height: 12px;
  border-radius: 2px;
  border: 1px solid #666666;
}

.entity-count {
  color: #999999;
  font-size: 12px;
  margin-left: auto;
}

.no-selection {
  text-align: center;
  color: #999999;
  padding: 20px;
}

.measure-results {
  margin: 15px 0;
  padding: 10px;
  background: #1a1a1a;
  border-radius: 4px;
}

.measure-results div {
  display: flex;
  justify-content: space-between;
  margin-bottom: 5px;
  color: #cccccc;
  font-size: 12px;
}

.measure-results label {
  color: #ffd700;
}

/* Element Plus 组件样式定制 */
:deep(.el-button.active) {
  background: #ffd700;
  border-color: #ffd700;
  color: #1a1a1a;
}

:deep(.el-tree) {
  background: transparent;
  color: #cccccc;
}

:deep(.el-tree-node__content) {
  background: transparent;
  color: #cccccc;
}

:deep(.el-tree-node__content:hover) {
  background: rgba(255, 215, 0, 0.1);
}

:deep(.el-checkbox__input.is-checked .el-checkbox__inner) {
  background-color: #ffd700;
  border-color: #ffd700;
}
</style>
