<template>
  <div class="data-list">
    <div class="data-header">
      <el-tabs v-model="activeTab" @tab-change="handleTabChange">
        <el-tab-pane label="地形数据" name="terrain">
          <template #label>
            <span class="tab-label">
              <el-icon><MapLocation /></el-icon>
              地形数据 ({{ terrainDataList.length }})
            </span>
          </template>
        </el-tab-pane>
        <el-tab-pane label="钻孔数据" name="drillhole">
          <template #label>
            <span class="tab-label">
              <el-icon><Position /></el-icon>
              钻孔数据 ({{ drillHoleDataList.length }})
            </span>
          </template>
        </el-tab-pane>
        <el-tab-pane label="地质数据" name="geology">
          <template #label>
            <span class="tab-label">
              <el-icon><Grid /></el-icon>
              地质数据 ({{ geologyDataList.length }})
            </span>
          </template>
        </el-tab-pane>
        <el-tab-pane label="CAD数据" name="cad">
          <template #label>
            <span class="tab-label">
              <el-icon><Files /></el-icon>
              CAD数据 ({{ cadDataList.length }})
            </span>
          </template>
        </el-tab-pane>
      </el-tabs>
      
      <div class="header-actions">
        <el-button type="primary" size="small" @click="showImportDialog">
          <el-icon><Plus /></el-icon>
          导入数据
        </el-button>
        <el-button type="success" size="small" @click="exportCurrentData">
          <el-icon><Download /></el-icon>
          导出
        </el-button>
      </div>
    </div>

    <div class="data-content">
      <!-- 地形数据列表 -->
      <div v-if="activeTab === 'terrain'" class="data-table">
        <el-table 
          :data="terrainDataList" 
          style="width: 100%" 
          size="small"
          :row-class-name="getRowClassName"
        >
          <el-table-column type="selection" width="55" />
          <el-table-column prop="name" label="名称" min-width="120">
            <template #default="{ row }">
              <div class="data-name">
                <el-icon class="type-icon"><MapLocation /></el-icon>
                {{ row.name }}
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="type" label="类型" width="100">
            <template #default="{ row }">
              <el-tag :type="getTerrainTypeTag(row.type)" size="small">
                {{ getTerrainTypeName(row.type) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="format" label="格式" width="80" />
          <el-table-column prop="size" label="大小" width="100">
            <template #default="{ row }">
              {{ formatFileSize(row.size) }}
            </template>
          </el-table-column>
          <el-table-column prop="uploadTime" label="上传时间" width="150">
            <template #default="{ row }">
              {{ formatDate(row.uploadTime) }}
            </template>
          </el-table-column>
          <el-table-column label="可见性" width="80">
            <template #default="{ row }">
              <el-switch 
                v-model="row.visible" 
                @change="toggleVisibility('terrain', row.id)"
              />
            </template>
          </el-table-column>
          <el-table-column label="透明度" width="120">
            <template #default="{ row }">
              <el-slider 
                v-model="row.opacity" 
                :min="0" 
                :max="100" 
                size="small"
                @change="updateOpacity('terrain', row.id, $event)"
              />
            </template>
          </el-table-column>
          <el-table-column label="操作" width="150" fixed="right">
            <template #default="{ row }">
              <el-button type="primary" size="small" @click="editData('terrain', row)">
                <el-icon><Edit /></el-icon>
              </el-button>
              <el-button type="info" size="small" @click="viewData('terrain', row)">
                <el-icon><View /></el-icon>
              </el-button>
              <el-button type="danger" size="small" @click="deleteData('terrain', row.id)">
                <el-icon><Delete /></el-icon>
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <!-- 钻孔数据列表 -->
      <div v-if="activeTab === 'drillhole'" class="data-table">
        <el-table 
          :data="drillHoleDataList" 
          style="width: 100%" 
          size="small"
          :row-class-name="getRowClassName"
        >
          <el-table-column type="selection" width="55" />
          <el-table-column prop="name" label="名称" min-width="120">
            <template #default="{ row }">
              <div class="data-name">
                <el-icon class="type-icon"><Position /></el-icon>
                {{ row.name }}
              </div>
            </template>
          </el-table-column>
          <el-table-column label="位置" width="200">
            <template #default="{ row }">
              {{ row.location.longitude.toFixed(6) }}, {{ row.location.latitude.toFixed(6) }}
            </template>
          </el-table-column>
          <el-table-column prop="depth" label="深度(m)" width="100" />
          <el-table-column prop="diameter" label="直径(mm)" width="100" />
          <el-table-column label="样本数" width="80">
            <template #default="{ row }">
              {{ row.samples.length }}
            </template>
          </el-table-column>
          <el-table-column prop="uploadTime" label="上传时间" width="150">
            <template #default="{ row }">
              {{ formatDate(row.uploadTime) }}
            </template>
          </el-table-column>
          <el-table-column label="可见性" width="80">
            <template #default="{ row }">
              <el-switch 
                v-model="row.visible" 
                @change="toggleVisibility('drillhole', row.id)"
              />
            </template>
          </el-table-column>
          <el-table-column label="操作" width="150" fixed="right">
            <template #default="{ row }">
              <el-button type="primary" size="small" @click="editData('drillhole', row)">
                <el-icon><Edit /></el-icon>
              </el-button>
              <el-button type="info" size="small" @click="viewData('drillhole', row)">
                <el-icon><View /></el-icon>
              </el-button>
              <el-button type="danger" size="small" @click="deleteData('drillhole', row.id)">
                <el-icon><Delete /></el-icon>
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <!-- 地质数据列表 -->
      <div v-if="activeTab === 'geology'" class="data-table">
        <el-table 
          :data="geologyDataList" 
          style="width: 100%" 
          size="small"
          :row-class-name="getRowClassName"
        >
          <el-table-column type="selection" width="55" />
          <el-table-column prop="name" label="名称" min-width="120">
            <template #default="{ row }">
              <div class="data-name">
                <el-icon class="type-icon"><Grid /></el-icon>
                {{ row.name }}
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="type" label="类型" width="100">
            <template #default="{ row }">
              <el-tag :type="getGeologyTypeTag(row.type)" size="small">
                {{ getGeologyTypeName(row.type) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column label="样式" width="120">
            <template #default="{ row }">
              <div class="style-preview">
                <div 
                  class="color-box" 
                  :style="{ backgroundColor: row.style.color }"
                ></div>
                <div 
                  class="fill-box" 
                  :style="{ backgroundColor: row.style.fillColor }"
                ></div>
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="uploadTime" label="上传时间" width="150">
            <template #default="{ row }">
              {{ formatDate(row.uploadTime) }}
            </template>
          </el-table-column>
          <el-table-column label="可见性" width="80">
            <template #default="{ row }">
              <el-switch 
                v-model="row.visible" 
                @change="toggleVisibility('geology', row.id)"
              />
            </template>
          </el-table-column>
          <el-table-column label="操作" width="150" fixed="right">
            <template #default="{ row }">
              <el-button type="primary" size="small" @click="editData('geology', row)">
                <el-icon><Edit /></el-icon>
              </el-button>
              <el-button type="info" size="small" @click="viewData('geology', row)">
                <el-icon><View /></el-icon>
              </el-button>
              <el-button type="danger" size="small" @click="deleteData('geology', row.id)">
                <el-icon><Delete /></el-icon>
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <!-- CAD数据列表 -->
      <div v-if="activeTab === 'cad'" class="data-table">
        <el-table 
          :data="cadDataList" 
          style="width: 100%" 
          size="small"
          :row-class-name="getRowClassName"
        >
          <el-table-column type="selection" width="55" />
          <el-table-column prop="name" label="名称" min-width="120">
            <template #default="{ row }">
              <div class="data-name">
                <el-icon class="type-icon"><Files /></el-icon>
                {{ row.name }}
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="format" label="格式" width="80">
            <template #default="{ row }">
              <el-tag size="small">{{ row.format.toUpperCase() }}</el-tag>
            </template>
          </el-table-column>
          <el-table-column label="图层数" width="80">
            <template #default="{ row }">
              {{ row.layers.length }}
            </template>
          </el-table-column>
          <el-table-column prop="uploadTime" label="上传时间" width="150">
            <template #default="{ row }">
              {{ formatDate(row.uploadTime) }}
            </template>
          </el-table-column>
          <el-table-column label="可见性" width="80">
            <template #default="{ row }">
              <el-switch 
                v-model="row.visible" 
                @change="toggleVisibility('cad', row.id)"
              />
            </template>
          </el-table-column>
          <el-table-column label="操作" width="150" fixed="right">
            <template #default="{ row }">
              <el-button type="primary" size="small" @click="editData('cad', row)">
                <el-icon><Edit /></el-icon>
              </el-button>
              <el-button type="info" size="small" @click="viewData('cad', row)">
                <el-icon><View /></el-icon>
              </el-button>
              <el-button type="danger" size="small" @click="deleteData('cad', row.id)">
                <el-icon><Delete /></el-icon>
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>

    <!-- 导入对话框 -->
    <el-dialog 
      v-model="importDialogVisible" 
      title="数据导入" 
      width="800px"
      :before-close="handleImportDialogClose"
    >
      <DataImport @import-success="handleImportSuccess" />
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  MapLocation, Position, Grid, Files, Plus, Download,
  Edit, View, Delete
} from '@element-plus/icons-vue'
import { useDataStore } from '@/stores/dataStore'
import DataImport from './DataImport.vue'

// 定义组件名称
defineOptions({
  name: 'DataList'
})

const dataStore = useDataStore()

// 响应式数据
const activeTab = ref('terrain')
const importDialogVisible = ref(false)

// 计算属性
const terrainDataList = computed(() => dataStore.terrainDataList)
const drillHoleDataList = computed(() => dataStore.drillHoleDataList)
const geologyDataList = computed(() => dataStore.geologyDataList)
const cadDataList = computed(() => dataStore.cadDataList)

// 方法
const handleTabChange = (tabName: string) => {
  dataStore.currentDataType = tabName as any
}

const showImportDialog = () => {
  importDialogVisible.value = true
}

const handleImportDialogClose = () => {
  importDialogVisible.value = false
}

const handleImportSuccess = () => {
  importDialogVisible.value = false
  ElMessage.success('数据导入成功！')
}

const exportCurrentData = () => {
  dataStore.exportData(activeTab.value as any)
}

const toggleVisibility = (type: string, id: string) => {
  // 根据类型调用相应的切换方法
  if (type === 'terrain') {
    dataStore.toggleTerrainDataVisibility(id)
  }
  // 其他类型的切换逻辑...
}

const updateOpacity = (type: string, id: string, opacity: number) => {
  if (type === 'terrain') {
    dataStore.updateTerrainData(id, { opacity })
  }
}

const editData = (type: string, data: any) => {
  console.log('Edit data:', type, data)
  ElMessage.info('编辑功能开发中...')
}

const viewData = (type: string, data: any) => {
  console.log('View data:', type, data)
  ElMessage.info('查看功能开发中...')
}

const deleteData = async (type: string, id: string) => {
  try {
    await ElMessageBox.confirm('确定要删除这条数据吗？', '确认删除', {
      type: 'warning'
    })
    
    if (type === 'terrain') {
      dataStore.removeTerrainData(id)
    } else if (type === 'drillhole') {
      dataStore.removeDrillHoleData(id)
    } else if (type === 'geology') {
      dataStore.removeGeologyData(id)
    } else if (type === 'cad') {
      dataStore.removeCADData(id)
    }
    
    ElMessage.success('删除成功！')
  } catch {
    // 用户取消删除
  }
}

// 工具函数
const getRowClassName = ({ row }: { row: any }) => {
  return row.visible ? 'visible-row' : 'hidden-row'
}

const getTerrainTypeTag = (type: string) => {
  const tagMap: Record<string, string> = {
    dem: 'primary',
    contour: 'success',
    point_cloud: 'warning'
  }
  return tagMap[type] || 'info'
}

const getTerrainTypeName = (type: string) => {
  const nameMap: Record<string, string> = {
    dem: 'DEM',
    contour: '等高线',
    point_cloud: '点云'
  }
  return nameMap[type] || type
}

const getGeologyTypeTag = (type: string) => {
  const tagMap: Record<string, string> = {
    stratum: 'primary',
    fault: 'danger',
    ore_body: 'warning'
  }
  return tagMap[type] || 'info'
}

const getGeologyTypeName = (type: string) => {
  const nameMap: Record<string, string> = {
    stratum: '地层',
    fault: '断层',
    ore_body: '矿体'
  }
  return nameMap[type] || type
}

const formatFileSize = (bytes: number) => {
  if (bytes === 0) return '0 B'
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

const formatDate = (date: Date) => {
  return new Date(date).toLocaleString('zh-CN')
}
</script>

<style scoped>
.data-list {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.data-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 0;
  border-bottom: 1px solid #404040;
}

.tab-label {
  display: flex;
  align-items: center;
  gap: 5px;
}

.header-actions {
  display: flex;
  gap: 10px;
}

.data-content {
  flex: 1;
  overflow: auto;
  padding: 10px 0;
}

.data-table {
  height: 100%;
}

.data-name {
  display: flex;
  align-items: center;
  gap: 8px;
}

.type-icon {
  color: #ffd700;
}

.style-preview {
  display: flex;
  gap: 5px;
}

.color-box,
.fill-box {
  width: 20px;
  height: 20px;
  border: 1px solid #404040;
  border-radius: 3px;
}

/* 表格行样式 */
:deep(.visible-row) {
  background: rgba(255, 215, 0, 0.05);
}

:deep(.hidden-row) {
  background: rgba(128, 128, 128, 0.1);
  opacity: 0.6;
}

/* Element Plus 组件样式定制 */
:deep(.el-table) {
  background: #1a1a1a;
  color: #cccccc;
}

:deep(.el-table th) {
  background: #2a2a2a;
  color: #ffd700;
  border-color: #404040;
}

:deep(.el-table td) {
  border-color: #404040;
}

:deep(.el-table--small .el-table__cell) {
  padding: 8px 0;
}

:deep(.el-tabs__item) {
  color: #cccccc;
}

:deep(.el-tabs__item.is-active) {
  color: #ffd700;
}

:deep(.el-tabs__nav-wrap::after) {
  background: #404040;
}

:deep(.el-tabs__active-bar) {
  background: #ffd700;
}
</style>
